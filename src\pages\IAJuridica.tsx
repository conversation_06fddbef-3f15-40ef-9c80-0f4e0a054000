import React, { useState, useRef, useEffect } from 'react'
import { DashboardLayout } from "@/components/dashboard-layout"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Brain, Send, Bot, User } from "lucide-react"
import { supabase } from "@/integrations/supabase/client"

interface Message {
  id: string
  content: string
  role: 'user' | 'assistant'
  timestamp: Date
}

interface AIChatComponentProps {
  messages?: Message[]
  onSendMessage?: (message: string) => void
  placeholder?: string
  className?: string
}

const AIChatComponent: React.FC<AIChatComponentProps> = ({
  messages: initialMessages = [
    {
      id: '1',
      content: 'Olá! Sou sua assistente jurídica inteligente. Como posso ajudá-lo hoje?',
      role: 'assistant',
      timestamp: new Date()
    }
  ],
  onSendMessage,
  placeholder = 'Digite sua pergunta jurídica...',
  className = ''
}) => {
  const [messages, setMessages] = useState<Message[]>(initialMessages)
  const [inputValue, setInputValue] = useState('')
  const [isTyping, setIsTyping] = useState(false)
  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputValue,
      role: 'user',
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    const currentInput = inputValue
    setInputValue('')
    setIsTyping(true)

    if (onSendMessage) {
      onSendMessage(currentInput)
    } else {
      try {
        // Prepare messages history for context
        const messagesHistory = [...messages, { content: currentInput, role: 'user' as const }];
        
        // Call Gemini AI through Supabase edge function with full conversation history
        const { data, error } = await supabase.functions.invoke('gemini-chat', {
          body: { messages: messagesHistory }
        });

        if (error) {
          throw new Error(error.message || 'Erro ao comunicar com a IA');
        }

        const aiResponse: Message = {
          id: (Date.now() + 1).toString(),
          content: data.response || 'Desculpe, não consegui processar sua pergunta. Tente novamente.',
          role: 'assistant',
          timestamp: new Date()
        }
        setMessages(prev => [...prev, aiResponse])
      } catch (error) {
        console.error('Erro ao consultar IA:', error)
        const errorResponse: Message = {
          id: (Date.now() + 1).toString(),
          content: 'Desculpe, ocorreu um erro ao processar sua pergunta. Verifique sua conexão e tente novamente.',
          role: 'assistant',
          timestamp: new Date()
        }
        setMessages(prev => [...prev, errorResponse])
      } finally {
        setIsTyping(false)
      }
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const handleQuickAction = (question: string) => {
    setInputValue(question)
  }

  return (
    <div className={`flex flex-col h-[calc(100vh-250px)] w-full max-w-4xl mx-auto bg-background border border-border rounded-lg shadow-lg overflow-hidden ${className}`}>
      {/* Header */}
      <div className="flex items-center gap-3 p-4 border-b border-border bg-background/50 backdrop-blur-sm">
        <div className="relative">
          <Avatar className="h-8 w-8 ring-2 ring-primary/20">
            <AvatarFallback className="bg-primary/10">
              <Bot className="h-4 w-4 text-primary" />
            </AvatarFallback>
          </Avatar>
          <div className="absolute -inset-1 bg-gradient-to-r from-primary/30 to-purple-500/30 rounded-full blur-sm animate-pulse" />
        </div>
        <div>
          <h3 className="font-semibold text-foreground">IA Jurídica</h3>
          <p className="text-xs text-muted-foreground">Online</p>
        </div>
      </div>

      {/* Messages */}
      <ScrollArea className="flex-1 p-4" ref={scrollAreaRef}>
        <div className="space-y-4">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex gap-3 ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              {message.role === 'assistant' && (
                <div className="relative">
                  <Avatar className="h-8 w-8 ring-2 ring-primary/20">
                    <AvatarFallback className="bg-primary/10">
                      <Bot className="h-4 w-4 text-primary" />
                    </AvatarFallback>
                  </Avatar>
                  <div className="absolute -inset-1 bg-gradient-to-r from-primary/20 to-purple-500/20 rounded-full blur-sm" />
                </div>
              )}
              
              <div
                className={`relative max-w-[80%] px-4 py-2 rounded-lg ${
                  message.role === 'user'
                    ? 'bg-primary text-primary-foreground ml-auto'
                    : 'bg-muted text-muted-foreground'
                }`}
              >
                {message.role === 'assistant' && (
                  <div className="absolute -inset-0.5 bg-gradient-to-r from-primary/30 to-purple-500/30 rounded-lg blur-sm opacity-75" />
                )}
                <div className="relative">
                  <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                  <span className="text-xs opacity-70 mt-1 block">
                    {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </span>
                </div>
              </div>

              {message.role === 'user' && (
                <Avatar className="h-8 w-8 ring-2 ring-primary/20">
                  <AvatarFallback className="bg-primary/10">
                    <User className="h-4 w-4 text-primary" />
                  </AvatarFallback>
                </Avatar>
              )}
            </div>
          ))}
          
          {isTyping && (
            <div className="flex gap-3 justify-start">
              <div className="relative">
                <Avatar className="h-8 w-8 ring-2 ring-primary/20">
                  <AvatarFallback className="bg-primary/10">
                    <Bot className="h-4 w-4 text-primary" />
                  </AvatarFallback>
                </Avatar>
                <div className="absolute -inset-1 bg-gradient-to-r from-primary/20 to-purple-500/20 rounded-full blur-sm animate-pulse" />
              </div>
              <div className="relative bg-muted px-4 py-2 rounded-lg">
                <div className="absolute -inset-0.5 bg-gradient-to-r from-primary/20 to-purple-500/20 rounded-lg blur-sm opacity-50" />
                <div className="relative flex gap-1">
                  <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                  <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                  <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
                </div>
              </div>
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>

      {/* Input */}
      <div className="p-4 border-t border-border bg-background/50 backdrop-blur-sm">
        <div className="relative flex gap-2">
          <div className="relative flex-1">
            <Input
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder={placeholder}
              className="pr-12 bg-background border-border focus:ring-2 focus:ring-primary/20 focus:border-primary transition-all duration-200"
            />
            <div className="absolute -inset-0.5 bg-gradient-to-r from-primary/20 to-purple-500/20 rounded-md blur-sm opacity-0 focus-within:opacity-100 transition-opacity duration-200 -z-10" />
          </div>
          <Button
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || isTyping}
            className="relative bg-primary hover:bg-primary/90 text-primary-foreground transition-all duration-200 group"
          >
            <div className="absolute -inset-0.5 bg-gradient-to-r from-primary/50 to-purple-500/50 rounded-md blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
            <Send className="h-4 w-4 relative" />
          </Button>
        </div>
        
        {/* Quick Actions */}
        <div className="mt-3 grid grid-cols-1 md:grid-cols-3 gap-2">
          <Button 
            variant="outline" 
            size="sm"
            className="justify-start text-left h-auto p-2 text-xs"
            onClick={() => handleQuickAction("Como calcular indenização por danos morais?")}
          >
            Como calcular indenização por danos morais?
          </Button>
          <Button 
            variant="outline" 
            size="sm"
            className="justify-start text-left h-auto p-2 text-xs"
            onClick={() => handleQuickAction("Requisitos para ação de cobrança")}
          >
            Requisitos para ação de cobrança
          </Button>
          <Button 
            variant="outline" 
            size="sm"
            className="justify-start text-left h-auto p-2 text-xs"
            onClick={() => handleQuickAction("Prazos para recurso em processo civil")}
          >
            Prazos para recurso em processo civil
          </Button>
        </div>
      </div>
    </div>
  )
}

export default function IAJuridica() {
  return (
    <DashboardLayout title="IA Jurídica">
      <div className="h-[calc(100vh-200px)] max-w-6xl mx-auto flex flex-col">
        <div className="text-center space-y-2 mb-6">
          <div className="flex items-center justify-center space-x-2">
            <Brain className="h-8 w-8 text-primary" />
            <h1 className="text-3xl font-bold">IA Jurídica</h1>
          </div>
          <p className="text-muted-foreground">
            Faça perguntas jurídicas e obtenha respostas especializadas
          </p>
        </div>

        <AIChatComponent />
      </div>
    </DashboardLayout>
  )
}