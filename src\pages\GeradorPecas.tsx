import { useState } from "react"
import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { GlowCard } from "@/components/ui/glow-card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Progress } from "@/components/ui/progress"
import { Gavel, FileText, Download, Brain, Star, Clock, CheckCircle, Copy, Edit } from "lucide-react"

export default function GeradorPecas() {
  const [isGenerating, setIsGenerating] = useState(false)
  const [generationProgress, setGenerationProgress] = useState(0)

  const pecasRecentes = [
    {
      id: 1,
      titulo: "Petição Inicial - Rescisão Indireta",
      tipo: "Petição Inicial",
      area: "Trabalhista",
      cliente: "Maria Silva",
      dataGeracao: "15/03/2024",
      status: "finalizada",
      qualidade: 95,
      paginas: 8,
      color: "blue" as const
    },
    {
      id: 2,
      titulo: "Contestação - Danos Morais",
      tipo: "Contestação",
      area: "Civil",
      cliente: "João Santos",
      dataGeracao: "14/03/2024",
      status: "revisao",
      qualidade: 87,
      paginas: 12,
      color: "green" as const
    },
    {
      id: 3,
      titulo: "Recurso Ordinário - FGTS",
      tipo: "Recurso",
      area: "Trabalhista",
      cliente: "Ana Costa",
      dataGeracao: "13/03/2024",
      status: "gerando",
      qualidade: 0,
      paginas: 0,
      color: "purple" as const
    }
  ]

  const modelos = [
    {
      nome: "Petição Inicial Cível",
      descricao: "Modelo para ações cíveis em geral",
      area: "Civil",
      usos: 45,
      avaliacao: 4.8
    },
    {
      nome: "Contestação Trabalhista",
      descricao: "Contestação para processos trabalhistas",
      area: "Trabalhista",
      usos: 32,
      avaliacao: 4.6
    },
    {
      nome: "Recurso Administrativo",
      descricao: "Recurso para órgãos administrativos",
      area: "Administrativo",
      usos: 28,
      avaliacao: 4.7
    },
    {
      nome: "Contrato de Prestação",
      descricao: "Contrato de prestação de serviços",
      area: "Empresarial",
      usos: 56,
      avaliacao: 4.9
    }
  ]

  const estatisticas = [
    { label: "Peças Geradas", valor: "234", icone: FileText },
    { label: "Tempo Médio", valor: "3.2min", icone: Clock },
    { label: "Qualidade Média", valor: "92.4%", icone: Star },
    { label: "Taxa de Aprovação", valor: "96.8%", icone: CheckCircle }
  ]

  return (
    <DashboardLayout title="Gerador de Peças">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Gerador de Peças Jurídicas</h2>
            <p className="text-muted-foreground">
              Crie peças jurídicas profissionais com inteligência artificial
            </p>
          </div>
          <Button>
            <Download className="mr-2 h-4 w-4" />
            Exportar Histórico
          </Button>
        </div>

        {/* Estatísticas */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {estatisticas.map((stat, i) => (
            <GlowCard key={i} glowColor={i % 2 === 0 ? "blue" : "green"} customSize className="w-full">
              <div className="p-6">
                <div className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">{stat.label}</CardTitle>
                  <stat.icone className="h-4 w-4 text-muted-foreground" />
                </div>
                <div>
                  <div className="text-2xl font-bold">{stat.valor}</div>
                </div>
              </div>
            </GlowCard>
          ))}
        </div>

        <div className="grid gap-6 lg:grid-cols-3">
          {/* Configuração e Geração */}
          <div className="lg:col-span-2 space-y-6">
            <GlowCard glowColor="blue" customSize className="w-full">
              <div className="p-6">
                <CardTitle className="flex items-center mb-4">
                  <Brain className="mr-2 h-4 w-4" />
                  Configuração da Peça
                </CardTitle>
                
                <Tabs defaultValue="basico" className="space-y-4">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="basico">Configuração Básica</TabsTrigger>
                    <TabsTrigger value="avancado">Configuração Avançada</TabsTrigger>
                  </TabsList>

                  <TabsContent value="basico" className="space-y-4">
                    <div className="grid gap-4 md:grid-cols-2">
                      <div className="space-y-2">
                        <Label>Tipo de Peça</Label>
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione o tipo" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="peticao-inicial">Petição Inicial</SelectItem>
                            <SelectItem value="contestacao">Contestação</SelectItem>
                            <SelectItem value="recurso">Recurso</SelectItem>
                            <SelectItem value="contrato">Contrato</SelectItem>
                            <SelectItem value="parecer">Parecer Jurídico</SelectItem>
                            <SelectItem value="acordo">Termo de Acordo</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label>Área do Direito</Label>
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione a área" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="civil">Civil</SelectItem>
                            <SelectItem value="penal">Penal</SelectItem>
                            <SelectItem value="trabalhista">Trabalhista</SelectItem>
                            <SelectItem value="tributario">Tributário</SelectItem>
                            <SelectItem value="administrativo">Administrativo</SelectItem>
                            <SelectItem value="empresarial">Empresarial</SelectItem>
                            <SelectItem value="familia">Família</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>Cliente/Parte</Label>
                      <Input placeholder="Nome do cliente ou parte envolvida" />
                    </div>

                    <div className="space-y-2">
                      <Label>Descrição do Caso</Label>
                      <Textarea 
                        placeholder="Descreva detalhadamente o caso, incluindo fatos relevantes, partes envolvidas, objetivos da peça e fundamentação jurídica desejada..."
                        className="min-h-[120px]"
                      />
                    </div>
                  </TabsContent>

                  <TabsContent value="avancado" className="space-y-4">
                    <div className="grid gap-4 md:grid-cols-2">
                      <div className="space-y-2">
                        <Label>Valor da Causa</Label>
                        <Input placeholder="R$ 0,00" />
                      </div>

                      <div className="space-y-2">
                        <Label>Competência</Label>
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="estadual">Justiça Estadual</SelectItem>
                            <SelectItem value="federal">Justiça Federal</SelectItem>
                            <SelectItem value="trabalho">Justiça do Trabalho</SelectItem>
                            <SelectItem value="eleitoral">Justiça Eleitoral</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>Fundamentação Legal</Label>
                      <Textarea 
                        placeholder="Especifique artigos de lei, jurisprudência e doutrina relevantes..."
                        className="min-h-[100px]"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Pedidos Específicos</Label>
                      <Textarea 
                        placeholder="Detalhe os pedidos que devem constar na peça..."
                        className="min-h-[100px]"
                      />
                    </div>
                  </TabsContent>
                </Tabs>

                {isGenerating && (
                  <div className="mt-6 space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Gerando peça...</span>
                      <span className="text-sm text-muted-foreground">{generationProgress}%</span>
                    </div>
                    <Progress value={generationProgress} className="w-full" />
                  </div>
                )}

                <Button className="w-full mt-6" disabled={isGenerating}>
                  <Gavel className="mr-2 h-4 w-4" />
                  {isGenerating ? 'Gerando...' : 'Gerar Peça'}
                </Button>
              </div>
            </GlowCard>

            {/* Peças Recentes */}
            <GlowCard glowColor="green" customSize className="w-full">
              <div className="p-6">
                <CardTitle className="flex items-center mb-4">
                  <FileText className="mr-2 h-4 w-4" />
                  Peças Recentes
                </CardTitle>
                <div className="space-y-4">
                  {pecasRecentes.map((peca) => (
                    <div key={peca.id} className="flex items-center justify-between p-4 bg-muted/20 rounded-lg">
                      <div className="flex items-center space-x-4">
                        <div className="w-10 h-10 bg-muted rounded-lg flex items-center justify-center">
                          <Gavel className="h-5 w-5" />
                        </div>
                        <div>
                          <p className="font-medium">{peca.titulo}</p>
                          <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                            <span>{peca.tipo}</span>
                            <span>•</span>
                            <span>{peca.area}</span>
                            <span>•</span>
                            <span>{peca.cliente}</span>
                          </div>
                          <p className="text-xs text-muted-foreground">{peca.dataGeracao}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <div className="text-right">
                          <Badge variant={
                            peca.status === 'finalizada' ? 'default' :
                            peca.status === 'revisao' ? 'secondary' : 'outline'
                          }>
                            {peca.status === 'finalizada' ? 'Finalizada' :
                             peca.status === 'revisao' ? 'Em Revisão' : 'Gerando...'}
                          </Badge>
                          {peca.qualidade > 0 && (
                            <p className="text-xs text-muted-foreground mt-1">
                              Qualidade: {peca.qualidade}%
                            </p>
                          )}
                        </div>
                        <div className="flex space-x-1">
                          <Button variant="outline" size="sm">
                            <Copy className="h-4 w-4" />
                          </Button>
                          <Button variant="outline" size="sm">
                            <Edit className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </GlowCard>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Modelos */}
            <GlowCard glowColor="purple" customSize className="w-full">
              <div className="p-6">
                <CardTitle className="mb-4">Modelos Populares</CardTitle>
                <div className="space-y-3">
                  {modelos.map((modelo, i) => (
                    <div key={i} className="p-3 bg-muted/20 rounded-lg">
                      <div className="flex items-start justify-between">
                        <div className="space-y-1">
                          <p className="font-medium text-sm">{modelo.nome}</p>
                          <p className="text-xs text-muted-foreground">{modelo.descricao}</p>
                          <div className="flex items-center space-x-2">
                            <Badge variant="outline">{modelo.area}</Badge>
                            <div className="flex items-center text-xs">
                              <Star className="h-3 w-3 mr-1 fill-yellow-400 text-yellow-400" />
                              {modelo.avaliacao}
                            </div>
                          </div>
                        </div>
                      </div>
                      <Button variant="ghost" size="sm" className="w-full mt-2 text-xs">
                        Usar Modelo
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            </GlowCard>

            {/* Dicas */}
            <GlowCard glowColor="orange" customSize className="w-full">
              <div className="p-6">
                <CardTitle className="mb-4">💡 Dicas para Melhores Resultados</CardTitle>
                <ul className="text-sm text-muted-foreground space-y-2">
                  <li>• Seja específico na descrição dos fatos</li>
                  <li>• Inclua informações sobre as partes envolvidas</li>
                  <li>• Mencione a fundamentação jurídica desejada</li>
                  <li>• Defina claramente os objetivos da peça</li>
                  <li>• Use modelos para acelerar o processo</li>
                </ul>
              </div>
            </GlowCard>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}