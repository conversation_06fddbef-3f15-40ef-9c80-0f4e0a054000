import { useState, useEffect } from 'react'
import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { GlowCard } from '@/components/ui/glow-card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Shield, Users, Lock, Key, Database, Settings } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface Permission {
  id: string
  name: string
  description: string
  enabled: boolean
  category: 'system' | 'data' | 'user' | 'admin'
}

interface Role {
  id: string
  name: string
  label: string
  description: string
  permissions: string[]
  userCount: number
}

export default function PermissoesGlobais() {
  const [permissions, setPermissions] = useState<Permission[]>([
    {
      id: 'create_users',
      name: '<PERSON><PERSON><PERSON>',
      description: 'Permite criar novos usuários no sistema',
      enabled: true,
      category: 'user'
    },
    {
      id: 'delete_users',
      name: 'Excluir Usuários',
      description: 'Permite excluir usuários do sistema',
      enabled: false,
      category: 'user'
    },
    {
      id: 'manage_organizations',
      name: 'Gerenciar Organizações',
      description: 'Permite criar e gerenciar organizações',
      enabled: true,
      category: 'admin'
    },
    {
      id: 'access_system_logs',
      name: 'Acessar Logs do Sistema',
      description: 'Permite visualizar logs de auditoria e sistema',
      enabled: true,
      category: 'system'
    },
    {
      id: 'backup_restore',
      name: 'Backup e Restauração',
      description: 'Permite realizar backup e restauração de dados',
      enabled: false,
      category: 'data'
    },
    {
      id: 'system_settings',
      name: 'Configurações do Sistema',
      description: 'Permite alterar configurações globais do sistema',
      enabled: true,
      category: 'system'
    }
  ])

  const [roles, setRoles] = useState<Role[]>([
    {
      id: 'owner',
      name: 'owner',
      label: 'Proprietário',
      description: 'Acesso total ao sistema com todas as permissões',
      permissions: ['create_users', 'delete_users', 'manage_organizations', 'access_system_logs', 'backup_restore', 'system_settings'],
      userCount: 1
    },
    {
      id: 'admin',
      name: 'admin',
      label: 'Administrador',
      description: 'Acesso administrativo com a maioria das permissões',
      permissions: ['create_users', 'manage_organizations', 'access_system_logs', 'system_settings'],
      userCount: 0
    },
    {
      id: 'lawyer',
      name: 'lawyer',
      label: 'Advogado',
      description: 'Acesso às funcionalidades jurídicas principais',
      permissions: ['create_users'],
      userCount: 0
    },
    {
      id: 'assistant',
      name: 'assistant',
      label: 'Assistente',
      description: 'Acesso limitado para assistir advogados',
      permissions: [],
      userCount: 0
    },
    {
      id: 'client',
      name: 'client',
      label: 'Cliente',
      description: 'Acesso básico do cliente',
      permissions: [],
      userCount: 0
    }
  ])

  const { toast } = useToast()

  const togglePermission = (permissionId: string) => {
    setPermissions(prev => 
      prev.map(perm => 
        perm.id === permissionId 
          ? { ...perm, enabled: !perm.enabled }
          : perm
      )
    )
    
    toast({
      title: "Permissão atualizada",
      description: "A permissão foi alterada com sucesso.",
    })
  }

  const toggleRolePermission = (roleId: string, permissionId: string) => {
    setRoles(prev =>
      prev.map(role => {
        if (role.id === roleId) {
          const hasPermission = role.permissions.includes(permissionId)
          const newPermissions = hasPermission
            ? role.permissions.filter(p => p !== permissionId)
            : [...role.permissions, permissionId]
          
          return { ...role, permissions: newPermissions }
        }
        return role
      })
    )

    toast({
      title: "Role atualizado",
      description: "As permissões do role foram atualizadas.",
    })
  }

  const getPermissionsByCategory = (category: string) => {
    return permissions.filter(perm => perm.category === category)
  }

  const getRoleBadgeVariant = (roleName: string) => {
    switch (roleName) {
      case 'owner': return 'default'
      case 'admin': return 'secondary'
      case 'lawyer': return 'outline'
      default: return 'outline'
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'system': return Settings
      case 'data': return Database
      case 'user': return Users
      case 'admin': return Shield
      default: return Key
    }
  }

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'system': return 'Sistema'
      case 'data': return 'Dados'
      case 'user': return 'Usuários'
      case 'admin': return 'Administração'
      default: return 'Outros'
    }
  }

  return (
    <DashboardLayout title="Permissões Globais">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-2">
          <Shield className="h-6 w-6 text-primary" />
          <h2 className="text-2xl font-bold">Permissões Globais</h2>
        </div>

        <Tabs defaultValue="permissions" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="permissions">Permissões do Sistema</TabsTrigger>
            <TabsTrigger value="roles">Roles e Hierarquia</TabsTrigger>
          </TabsList>

          <TabsContent value="permissions" className="space-y-6">
            {/* System Permissions */}
            {['system', 'admin', 'user', 'data'].map(category => {
              const categoryPermissions = getPermissionsByCategory(category)
              const Icon = getCategoryIcon(category)
              
              return (
                <Card key={category}>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Icon className="h-5 w-5" />
                      Permissões de {getCategoryLabel(category)}
                    </CardTitle>
                    <CardDescription>
                      Configure as permissões relacionadas a {getCategoryLabel(category).toLowerCase()}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {categoryPermissions.map((permission) => (
                        <div key={permission.id} className="flex items-center justify-between p-4 border rounded-lg">
                          <div className="flex-1">
                            <div className="flex items-center gap-2">
                              <Label htmlFor={permission.id} className="font-medium">
                                {permission.name}
                              </Label>
                              <Badge variant={permission.enabled ? 'default' : 'secondary'}>
                                {permission.enabled ? 'Ativa' : 'Inativa'}
                              </Badge>
                            </div>
                            <p className="text-sm text-muted-foreground mt-1">
                              {permission.description}
                            </p>
                          </div>
                          <Switch
                            id={permission.id}
                            checked={permission.enabled}
                            onCheckedChange={() => togglePermission(permission.id)}
                          />
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </TabsContent>

          <TabsContent value="roles" className="space-y-6">
            {/* Roles Management */}
            <div className="grid gap-6">
              {roles.map((role) => (
                <Card key={role.id}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Badge variant={getRoleBadgeVariant(role.name)}>
                          {role.label}
                        </Badge>
                        <span className="text-sm text-muted-foreground">
                          {role.userCount} usuário(s)
                        </span>
                      </div>
                      <Button variant="outline" size="sm">
                        Editar Role
                      </Button>
                    </div>
                    <CardDescription>
                      {role.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <Label className="text-sm font-medium">Permissões Atribuídas:</Label>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-2">
                          {permissions.map((permission) => (
                            <div key={permission.id} className="flex items-center space-x-2">
                              <Switch
                                id={`${role.id}-${permission.id}`}
                                checked={role.permissions.includes(permission.id)}
                                onCheckedChange={() => toggleRolePermission(role.id, permission.id)}
                                disabled={role.name === 'owner'} // Owner sempre tem todas as permissões
                              />
                              <Label 
                                htmlFor={`${role.id}-${permission.id}`}
                                className={`text-sm ${role.permissions.includes(permission.id) ? 'font-medium' : 'text-muted-foreground'}`}
                              >
                                {permission.name}
                              </Label>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>

        {/* Save Changes */}
        <Card>
          <CardHeader>
            <CardTitle>Salvar Alterações</CardTitle>
            <CardDescription>
              As alterações nas permissões entrarão em vigor imediatamente para todos os usuários
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex gap-3">
              <Button>
                Aplicar Alterações
              </Button>
              <Button variant="outline">
                Reverter Mudanças
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}