-- Create system settings table
CREATE TABLE public.system_settings (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  key TEXT NOT NULL UNIQUE,
  value JSONB NOT NULL,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_by UUID REFERENCES auth.users(id)
);

-- Enable RLS
ALTER TABLE public.system_settings ENABLE ROW LEVEL SECURITY;

-- Create policies for system settings (only owners can manage)
CREATE POLICY "Only owners can view system settings" 
ON public.system_settings 
FOR SELECT 
USING (has_role(auth.uid(), 'owner'::app_role));

CREATE POLICY "Only owners can insert system settings" 
ON public.system_settings 
FOR INSERT 
WITH CHECK (has_role(auth.uid(), 'owner'::app_role));

CREATE POLICY "Only owners can update system settings" 
ON public.system_settings 
FOR UPDATE 
USING (has_role(auth.uid(), 'owner'::app_role));

CREATE POLICY "Only owners can delete system settings" 
ON public.system_settings 
FOR DELETE 
USING (has_role(auth.uid(), 'owner'::app_role));

-- Create trigger for automatic timestamp updates
CREATE TRIGGER update_system_settings_updated_at
BEFORE UPDATE ON public.system_settings
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

-- Insert default system settings
INSERT INTO public.system_settings (key, value, description) VALUES
('site_name', '"Advocacia Jurídica"', 'Nome do sistema'),
('site_description', '"Sistema de gestão jurídica completo"', 'Descrição do sistema'),
('maintenance_mode', 'false', 'Modo de manutenção ativado'),
('registration_enabled', 'true', 'Registro de novos usuários habilitado'),
('email_verification_required', 'true', 'Verificação de email obrigatória'),
('max_file_size', '10', 'Tamanho máximo de arquivo em MB'),
('session_timeout', '60', 'Timeout da sessão em minutos'),
('backup_enabled', 'true', 'Backup automático habilitado'),
('backup_frequency', '"daily"', 'Frequência de backup'),
('email_provider', '"smtp"', 'Provedor de email'),
('smtp_host', '""', 'Servidor SMTP'),
('smtp_port', '587', 'Porta SMTP'),
('smtp_user', '""', 'Usuário SMTP'),
('timezone', '"America/Sao_Paulo"', 'Fuso horário do sistema'),
('language', '"pt-BR"', 'Idioma do sistema'),
('currency', '"BRL"', 'Moeda do sistema'),
('password_min_length', '8', 'Tamanho mínimo da senha'),
('password_require_special_chars', 'true', 'Exigir caracteres especiais na senha'),
('password_require_numbers', 'true', 'Exigir números na senha'),
('password_require_uppercase', 'true', 'Exigir maiúsculas na senha'),
('two_factor_enabled', 'false', 'Autenticação de dois fatores habilitada'),
('max_login_attempts', '5', 'Máximo de tentativas de login'),
('ip_whitelist_enabled', 'false', 'Lista de IPs permitidos habilitada'),
('ip_whitelist', '""', 'Lista de IPs permitidos'),
('cors_enabled', 'true', 'CORS habilitado'),
('cors_origins', '"https://localhost:3000"', 'Origens CORS permitidas');