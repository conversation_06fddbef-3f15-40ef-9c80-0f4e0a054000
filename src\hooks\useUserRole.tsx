import { useState, useEffect } from 'react'
import { supabase } from '@/integrations/supabase/client'
import { useAuth } from './useAuth'

type UserRole = 'owner' | 'admin' | 'lawyer' | 'assistant' | 'client' | null

export function useUserRole() {
  const { user } = useAuth()
  const [role, setRole] = useState<UserRole>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function fetchUserRole() {
      if (!user) {
        setRole(null)
        setLoading(false)
        return
      }

      try {
        const { data, error } = await supabase
          .from('user_roles')
          .select('role')
          .eq('user_id', user.id)
          .single()

        if (error) {
          console.error('Error fetching user role:', error)
          setRole(null)
        } else {
          setRole(data.role)
        }
      } catch (error) {
        console.error('Error:', error)
        setRole(null)
      } finally {
        setLoading(false)
      }
    }

    fetchUserRole()
  }, [user])

  const isOwner = role === 'owner'
  const isAdmin = role === 'admin' || role === 'owner'
  const isLawyer = role === 'lawyer' || role === 'admin' || role === 'owner'

  return {
    role,
    loading,
    isOwner,
    isAdmin,
    isLawyer,
  }
}