import { useState } from "react"
import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { GlowCard } from "@/components/ui/glow-card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Upload, FileText, Brain, Download, CheckCircle, Clock, AlertCircle, File, Eye } from "lucide-react"

export default function AnaliseDocumental() {
  const [uploadProgress, setUploadProgress] = useState(0)
  const [isAnalyzing, setIsAnalyzing] = useState(false)

  const documentosAnalisados = [
    {
      id: 1,
      nome: "Contrato_Trabalho_Silva.pdf",
      tipo: "Contrato",
      status: "concluido",
      dataAnalise: "15/03/2024",
      precisao: 94,
      insights: 12,
      tamanho: "2.3 MB",
      color: "green" as const
    },
    {
      id: 2,
      nome: "Peticao_Inicial_Santos.docx",
      tipo: "Petição",
      status: "processando",
      dataAnalise: "15/03/2024",
      precisao: 0,
      insights: 0,
      tamanho: "1.8 MB",
      color: "blue" as const
    },
    {
      id: 3,
      nome: "Documentos_Processo_123.zip",
      tipo: "Múltiplos",
      status: "erro",
      dataAnalise: "14/03/2024",
      precisao: 0,
      insights: 0,
      tamanho: "15.2 MB",
      color: "orange" as const
    }
  ]

  const tiposSuportados = [
    { tipo: "PDF", icone: "📄", descricao: "Documentos PDF digitalizados ou nativos" },
    { tipo: "DOC/DOCX", icone: "📝", descricao: "Documentos do Microsoft Word" },
    { tipo: "TXT", icone: "📃", descricao: "Arquivos de texto simples" },
    { tipo: "RTF", icone: "📋", descricao: "Rich Text Format" }
  ]

  const insights = [
    { categoria: "Cláusulas Importantes", quantidade: 8, cor: "blue" },
    { categoria: "Datas e Prazos", quantidade: 5, cor: "green" },
    { categoria: "Valores Monetários", quantidade: 12, cor: "purple" },
    { categoria: "Partes Envolvidas", quantidade: 4, cor: "orange" }
  ]

  return (
    <DashboardLayout title="Análise Documental">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Análise Documental com IA</h2>
            <p className="text-muted-foreground">
              Extraia informações importantes de documentos jurídicos automaticamente
            </p>
          </div>
          <Button>
            <Download className="mr-2 h-4 w-4" />
            Exportar Relatório
          </Button>
        </div>

        {/* Estatísticas */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <GlowCard glowColor="blue" customSize className="w-full">
            <div className="p-6">
              <div className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Documentos Analisados</CardTitle>
                <FileText className="h-4 w-4 text-muted-foreground" />
              </div>
              <div>
                <div className="text-2xl font-bold">47</div>
                <p className="text-xs text-muted-foreground">+12 este mês</p>
              </div>
            </div>
          </GlowCard>

          <GlowCard glowColor="green" customSize className="w-full">
            <div className="p-6">
              <div className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Precisão Média</CardTitle>
                <Brain className="h-4 w-4 text-muted-foreground" />
              </div>
              <div>
                <div className="text-2xl font-bold">96.8%</div>
                <p className="text-xs text-muted-foreground">+2.1% em relação ao mês passado</p>
              </div>
            </div>
          </GlowCard>

          <GlowCard glowColor="purple" customSize className="w-full">
            <div className="p-6">
              <div className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Insights Extraídos</CardTitle>
                <CheckCircle className="h-4 w-4 text-muted-foreground" />
              </div>
              <div>
                <div className="text-2xl font-bold">342</div>
                <p className="text-xs text-muted-foreground">informações importantes</p>
              </div>
            </div>
          </GlowCard>

          <GlowCard glowColor="orange" customSize className="w-full">
            <div className="p-6">
              <div className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Tempo Médio</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </div>
              <div>
                <div className="text-2xl font-bold">2.4min</div>
                <p className="text-xs text-muted-foreground">por documento</p>
              </div>
            </div>
          </GlowCard>
        </div>

        <div className="grid gap-6 lg:grid-cols-3">
          {/* Upload e Análise */}
          <div className="lg:col-span-2 space-y-6">
            <GlowCard glowColor="blue" customSize className="w-full">
              <div className="p-6">
                <CardTitle className="flex items-center mb-4">
                  <Upload className="mr-2 h-4 w-4" />
                  Upload de Documentos
                </CardTitle>
                <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center">
                  <Upload className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                  <p className="text-lg font-medium mb-2">
                    Arraste documentos aqui ou clique para selecionar
                  </p>
                  <p className="text-sm text-muted-foreground mb-4">
                    Suporte para PDF, DOC, DOCX, TXT (máx. 50MB por arquivo)
                  </p>
                  {uploadProgress > 0 && (
                    <div className="mb-4">
                      <Progress value={uploadProgress} className="w-full" />
                      <p className="text-xs text-muted-foreground mt-2">
                        Enviando... {uploadProgress}%
                      </p>
                    </div>
                  )}
                  <div className="flex gap-2 justify-center">
                    <Button variant="outline">
                      <Upload className="mr-2 h-4 w-4" />
                      Selecionar Arquivos
                    </Button>
                    <Button disabled={!isAnalyzing}>
                      <Brain className="mr-2 h-4 w-4" />
                      Analisar com IA
                    </Button>
                  </div>
                </div>
              </div>
            </GlowCard>

            {/* Documentos Analisados */}
            <GlowCard glowColor="green" customSize className="w-full">
              <div className="p-6">
                <CardTitle className="flex items-center mb-4">
                  <FileText className="mr-2 h-4 w-4" />
                  Documentos Recentes
                </CardTitle>
                <div className="space-y-4">
                  {documentosAnalisados.map((doc) => (
                    <div key={doc.id} className="flex items-center justify-between p-4 bg-muted/20 rounded-lg">
                      <div className="flex items-center space-x-4">
                        <div className="w-10 h-10 bg-muted rounded-lg flex items-center justify-center">
                          <File className="h-5 w-5" />
                        </div>
                        <div>
                          <p className="font-medium">{doc.nome}</p>
                          <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                            <span>{doc.tipo}</span>
                            <span>•</span>
                            <span>{doc.tamanho}</span>
                            <span>•</span>
                            <span>{doc.dataAnalise}</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <div className="text-right">
                          <Badge variant={
                            doc.status === 'concluido' ? 'default' :
                            doc.status === 'processando' ? 'secondary' : 'destructive'
                          }>
                            {doc.status === 'concluido' ? (
                              <><CheckCircle className="w-3 h-3 mr-1" /> Concluído</>
                            ) : doc.status === 'processando' ? (
                              <><Clock className="w-3 h-3 mr-1" /> Processando</>
                            ) : (
                              <><AlertCircle className="w-3 h-3 mr-1" /> Erro</>
                            )}
                          </Badge>
                          {doc.precisao > 0 && (
                            <p className="text-xs text-muted-foreground mt-1">
                              Precisão: {doc.precisao}%
                            </p>
                          )}
                        </div>
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </GlowCard>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Tipos Suportados */}
            <GlowCard glowColor="purple" customSize className="w-full">
              <div className="p-6">
                <CardTitle className="mb-4">Tipos Suportados</CardTitle>
                <div className="space-y-3">
                  {tiposSuportados.map((tipo, i) => (
                    <div key={i} className="flex items-start space-x-3">
                      <span className="text-2xl">{tipo.icone}</span>
                      <div>
                        <p className="font-medium text-sm">{tipo.tipo}</p>
                        <p className="text-xs text-muted-foreground">{tipo.descricao}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </GlowCard>

            {/* Insights por Categoria */}
            <GlowCard glowColor="orange" customSize className="w-full">
              <div className="p-6">
                <CardTitle className="mb-4">Insights por Categoria</CardTitle>
                <div className="space-y-3">
                  {insights.map((insight, i) => (
                    <div key={i} className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-sm">{insight.categoria}</p>
                        <p className="text-xs text-muted-foreground">identificados</p>
                      </div>
                      <Badge variant="outline">
                        {insight.quantidade}
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>
            </GlowCard>

            {/* Dicas */}
            <GlowCard glowColor="blue" customSize className="w-full">
              <div className="p-6">
                <CardTitle className="mb-4">💡 Dicas</CardTitle>
                <ul className="text-sm text-muted-foreground space-y-2">
                  <li>• Documentos com melhor qualidade geram análises mais precisas</li>
                  <li>• PDFs nativos têm melhor precisão que digitalizados</li>
                  <li>• Agrupe documentos relacionados para análise contextual</li>
                </ul>
              </div>
            </GlowCard>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}