// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://jjyjfgzkbryycgurltgm.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpqeWpmZ3prYnJ5eWNndXJsdGdtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI4OTQ2NDUsImV4cCI6MjA2ODQ3MDY0NX0.7Z2pVs0QO0bjU24NGD57tMuDmj3hy8jE6knjDwQK8BI";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});