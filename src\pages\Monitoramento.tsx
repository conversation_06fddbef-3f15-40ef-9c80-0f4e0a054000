import { useState, useEffect } from 'react'
import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { GlowCard } from '@/components/ui/glow-card'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Activity, Server, Database, Wifi, Cpu, HardDrive, AlertTriangle, CheckCircle, XCircle, RefreshCw } from 'lucide-react'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area } from 'recharts'

const performanceData = [
  { time: '00:00', cpu: 45, memory: 62, disk: 34, network: 123 },
  { time: '04:00', cpu: 52, memory: 68, disk: 36, network: 145 },
  { time: '08:00', cpu: 78, memory: 75, disk: 42, network: 234 },
  { time: '12:00', cpu: 85, memory: 82, disk: 45, network: 289 },
  { time: '16:00', cpu: 69, memory: 71, disk: 38, network: 198 },
  { time: '20:00', cpu: 58, memory: 65, disk: 35, network: 167 }
]

const systemServices = [
  { name: 'API Principal', status: 'active', uptime: '99.9%', lastCheck: '30s', responseTime: '45ms' },
  { name: 'Base de Dados', status: 'active', uptime: '99.8%', lastCheck: '15s', responseTime: '12ms' },
  { name: 'Sistema de Autenticação', status: 'active', uptime: '99.9%', lastCheck: '45s', responseTime: '23ms' },
  { name: 'Processador de Arquivos', status: 'warning', uptime: '98.2%', lastCheck: '2m', responseTime: '156ms' },
  { name: 'Email Service', status: 'active', uptime: '99.5%', lastCheck: '1m', responseTime: '78ms' },
  { name: 'Backup Automático', status: 'error', uptime: '95.1%', lastCheck: '5m', responseTime: 'N/A' }
]

const recentAlerts = [
  { id: 1, type: 'warning', message: 'CPU usage above 80% for 5 minutes', time: '2 min ago', service: 'API Principal' },
  { id: 2, type: 'error', message: 'Backup service failed to start', time: '15 min ago', service: 'Backup Automático' },
  { id: 3, type: 'info', message: 'System maintenance completed successfully', time: '1 hour ago', service: 'Sistema' },
  { id: 4, type: 'warning', message: 'High memory usage detected', time: '2 hours ago', service: 'Base de Dados' },
  { id: 5, type: 'error', message: 'Failed to send notification emails', time: '3 hours ago', service: 'Email Service' }
]

export default function Monitoramento() {
  const [lastUpdate, setLastUpdate] = useState(new Date())
  const [autoRefresh, setAutoRefresh] = useState(true)

  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(() => {
        setLastUpdate(new Date())
      }, 30000) // Atualiza a cada 30 segundos

      return () => clearInterval(interval)
    }
  }, [autoRefresh])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />
      case 'error':
        return <XCircle className="h-4 w-4 text-red-600" />
      default:
        return <CheckCircle className="h-4 w-4 text-gray-400" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="default" className="bg-green-100 text-green-800">Ativo</Badge>
      case 'warning':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Atenção</Badge>
      case 'error':
        return <Badge variant="destructive">Erro</Badge>
      default:
        return <Badge variant="outline">Desconhecido</Badge>
    }
  }

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'error':
        return <XCircle className="h-4 w-4 text-red-600" />
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />
      case 'info':
        return <CheckCircle className="h-4 w-4 text-blue-600" />
      default:
        return <CheckCircle className="h-4 w-4 text-gray-400" />
    }
  }

  const getCurrentMetrics = () => {
    const latest = performanceData[performanceData.length - 1]
    return {
      cpu: latest.cpu,
      memory: latest.memory,
      disk: latest.disk,
      network: latest.network
    }
  }

  const metrics = getCurrentMetrics()

  return (
    <DashboardLayout title="Monitoramento do Sistema">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Activity className="h-6 w-6 text-primary" />
            <h2 className="text-2xl font-bold">Monitoramento do Sistema</h2>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline">
              Última atualização: {lastUpdate.toLocaleTimeString('pt-BR')}
            </Badge>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => setLastUpdate(new Date())}
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Atualizar
            </Button>
          </div>
        </div>

        {/* System Status Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <GlowCard customSize className="h-auto" glowColor={metrics.cpu > 80 ? "red" : "green"}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">CPU</p>
                <p className="text-2xl font-bold">{metrics.cpu}%</p>
                <p className={`text-sm ${metrics.cpu > 80 ? 'text-red-600' : 'text-green-600'}`}>
                  {metrics.cpu > 80 ? 'Alto uso' : 'Normal'}
                </p>
              </div>
              <Cpu className={`h-8 w-8 ${metrics.cpu > 80 ? 'text-red-600' : 'text-green-600'}`} />
            </div>
          </GlowCard>

          <GlowCard customSize className="h-auto" glowColor={metrics.memory > 85 ? "red" : "green"}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Memória</p>
                <p className="text-2xl font-bold">{metrics.memory}%</p>
                <p className={`text-sm ${metrics.memory > 85 ? 'text-red-600' : 'text-green-600'}`}>
                  {metrics.memory > 85 ? 'Alto uso' : 'Normal'}
                </p>
              </div>
              <Database className={`h-8 w-8 ${metrics.memory > 85 ? 'text-red-600' : 'text-green-600'}`} />
            </div>
          </GlowCard>

          <GlowCard customSize className="h-auto" glowColor={metrics.disk > 90 ? "red" : "green"}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Disco</p>
                <p className="text-2xl font-bold">{metrics.disk}%</p>
                <p className={`text-sm ${metrics.disk > 90 ? 'text-red-600' : 'text-green-600'}`}>
                  {metrics.disk > 90 ? 'Quase cheio' : 'Normal'}
                </p>
              </div>
              <HardDrive className={`h-8 w-8 ${metrics.disk > 90 ? 'text-red-600' : 'text-green-600'}`} />
            </div>
          </GlowCard>

          <GlowCard customSize className="h-auto">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Rede</p>
                <p className="text-2xl font-bold">{metrics.network}</p>
                <p className="text-sm text-muted-foreground">MB/s</p>
              </div>
              <Wifi className="h-8 w-8 text-blue-600" />
            </div>
          </GlowCard>
        </div>

        <Tabs defaultValue="performance" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="performance">Performance</TabsTrigger>
            <TabsTrigger value="services">Serviços</TabsTrigger>
            <TabsTrigger value="alerts">Alertas</TabsTrigger>
            <TabsTrigger value="logs">Logs</TabsTrigger>
          </TabsList>

          <TabsContent value="performance" className="space-y-6">
            {/* Performance Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>CPU e Memória</CardTitle>
                  <CardDescription>
                    Uso de CPU e memória nas últimas 24 horas
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={performanceData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="time" />
                      <YAxis />
                      <Tooltip formatter={(value, name) => [`${value}%`, name === 'cpu' ? 'CPU' : 'Memória']} />
                      <Line type="monotone" dataKey="cpu" stroke="#8884d8" strokeWidth={2} name="cpu" />
                      <Line type="monotone" dataKey="memory" stroke="#82ca9d" strokeWidth={2} name="memory" />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Tráfego de Rede</CardTitle>
                  <CardDescription>
                    Tráfego de rede em MB/s
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <AreaChart data={performanceData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="time" />
                      <YAxis />
                      <Tooltip formatter={(value) => [`${value} MB/s`, 'Tráfego']} />
                      <Area type="monotone" dataKey="network" stroke="#ffc658" fill="#ffc658" fillOpacity={0.3} />
                    </AreaChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="services" className="space-y-6">
            {/* Services Status */}
            <Card>
              <CardHeader>
                <CardTitle>Status dos Serviços</CardTitle>
                <CardDescription>
                  Monitoramento em tempo real de todos os serviços do sistema
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Serviço</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Uptime</TableHead>
                      <TableHead>Última Verificação</TableHead>
                      <TableHead>Tempo de Resposta</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {systemServices.map((service, index) => (
                      <TableRow key={index}>
                        <TableCell className="font-medium">
                          <div className="flex items-center gap-2">
                            {getStatusIcon(service.status)}
                            {service.name}
                          </div>
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(service.status)}
                        </TableCell>
                        <TableCell>{service.uptime}</TableCell>
                        <TableCell>{service.lastCheck}</TableCell>
                        <TableCell>
                          <Badge variant={service.responseTime === 'N/A' ? 'destructive' : 'outline'}>
                            {service.responseTime}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="alerts" className="space-y-6">
            {/* Alerts */}
            <Card>
              <CardHeader>
                <CardTitle>Alertas Recentes</CardTitle>
                <CardDescription>
                  Alertas e notificações do sistema nas últimas horas
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentAlerts.map((alert) => (
                    <div key={alert.id} className="flex items-start gap-3 p-4 border rounded-lg">
                      {getAlertIcon(alert.type)}
                      <div className="flex-1">
                        <p className="font-medium">{alert.message}</p>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge variant="outline" className="text-xs">
                            {alert.service}
                          </Badge>
                          <span className="text-xs text-muted-foreground">
                            {alert.time}
                          </span>
                        </div>
                      </div>
                      <Button variant="ghost" size="sm">
                        Resolver
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="logs" className="space-y-6">
            {/* System Logs */}
            <Card>
              <CardHeader>
                <CardTitle>Logs do Sistema</CardTitle>
                <CardDescription>
                  Logs recentes de atividade do sistema
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="bg-black text-green-400 p-4 rounded-lg font-mono text-sm max-h-96 overflow-y-auto">
                  <div>[2024-01-15 14:23:45] INFO: Sistema iniciado com sucesso</div>
                  <div>[2024-01-15 14:23:46] INFO: Conectado ao banco de dados</div>
                  <div>[2024-01-15 14:24:12] INFO: Usuário <EMAIL> fez login</div>
                  <div>[2024-01-15 14:25:33] WARNING: CPU usage acima de 80%</div>
                  <div>[2024-01-15 14:26:45] INFO: Backup automático iniciado</div>
                  <div>[2024-01-15 14:27:12] ERROR: Falha no serviço de backup</div>
                  <div>[2024-01-15 14:28:01] INFO: Tentativa de reconexão com o serviço de backup</div>
                  <div>[2024-01-15 14:28:45] INFO: API request: GET /api/users - 200ms</div>
                  <div>[2024-01-15 14:29:12] INFO: Nova organização criada: ID 123</div>
                  <div>[2024-01-15 14:30:01] WARNING: Memória acima de 85%</div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}