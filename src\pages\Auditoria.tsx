import { useState } from 'react'
import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { GlowCard } from '@/components/ui/glow-card'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

import { FileText, Search, Filter, Download, Eye, User, Settings, Database, Shield } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

const auditLogs = [
  {
    id: '1',
    timestamp: '2024-01-15 14:30:45',
    user: '<EMAIL>',
    action: 'LOGIN',
    resource: 'Sistema',
    details: 'Login realizado com sucesso',
    ip: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
    severity: 'info'
  },
  {
    id: '2',
    timestamp: '2024-01-15 14:25:12',
    user: '<EMAIL>',
    action: 'CREATE',
    resource: 'Organização',
    details: 'Nova organização criada: "Advocacia Silva & Santos"',
    ip: '*************',
    userAgent: 'Mozilla/5.0 (Mac OS X 10_15_7)',
    severity: 'info'
  },
  {
    id: '3',
    timestamp: '2024-01-15 14:20:33',
    user: '<EMAIL>',
    action: 'UPDATE',
    resource: 'Usuário',
    details: 'Role alterado para administrador: <EMAIL>',
    ip: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
    severity: 'warning'
  },
  {
    id: '4',
    timestamp: '2024-01-15 14:15:21',
    user: 'system',
    action: 'BACKUP',
    resource: 'Base de Dados',
    details: 'Backup automático realizado com sucesso',
    ip: 'localhost',
    userAgent: 'Sistema',
    severity: 'info'
  },
  {
    id: '5',
    timestamp: '2024-01-15 14:10:45',
    user: '<EMAIL>',
    action: 'DELETE',
    resource: 'Usuário',
    details: 'Usuário removido: <EMAIL>',
    ip: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
    severity: 'critical'
  },
  {
    id: '6',
    timestamp: '2024-01-15 14:05:12',
    user: '<EMAIL>',
    action: 'FAILED_LOGIN',
    resource: 'Sistema',
    details: 'Tentativa de login falhada - senha incorreta',
    ip: '*********',
    userAgent: 'Mozilla/5.0 (iPhone)',
    severity: 'warning'
  }
]

const securityEvents = [
  {
    id: '1',
    timestamp: '2024-01-15 14:35:12',
    type: 'SUSPICIOUS_LOGIN',
    description: 'Múltiplas tentativas de login de IP suspeito',
    ip: '***************',
    severity: 'high',
    status: 'active'
  },
  {
    id: '2',
    timestamp: '2024-01-15 14:20:45',
    type: 'PRIVILEGE_ESCALATION',
    description: 'Usuário teve privilégios elevados',
    ip: '*************',
    severity: 'medium',
    status: 'resolved'
  },
  {
    id: '3',
    timestamp: '2024-01-15 14:10:30',
    type: 'DATA_ACCESS',
    description: 'Acesso a dados sensíveis fora do horário comercial',
    ip: '*************',
    severity: 'medium',
    status: 'investigating'
  }
]

const complianceReports = [
  {
    id: '1',
    name: 'Relatório LGPD - Janeiro 2024',
    type: 'LGPD',
    generatedAt: '2024-01-15 09:00:00',
    period: 'Janeiro 2024',
    status: 'completed',
    size: '2.3 MB'
  },
  {
    id: '2',
    name: 'Auditoria de Segurança - Dezembro 2023',
    type: 'Security',
    generatedAt: '2024-01-01 08:00:00',
    period: 'Dezembro 2023',
    status: 'completed',
    size: '5.7 MB'
  },
  {
    id: '3',
    name: 'Relatório de Acesso - Trimestre Q4 2023',
    type: 'Access',
    generatedAt: '2024-01-01 10:00:00',
    period: 'Q4 2023',
    status: 'completed',
    size: '1.8 MB'
  }
]

export default function Auditoria() {
  const [searchTerm, setSearchTerm] = useState('')
  const [actionFilter, setActionFilter] = useState('all')
  const [severityFilter, setSeverityFilter] = useState('all')
  const [selectedDateRange, setSelectedDateRange] = useState<any>(null)
  const { toast } = useToast()

  const getSeverityBadge = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <Badge variant="destructive">Crítico</Badge>
      case 'high':
        return <Badge variant="destructive">Alto</Badge>
      case 'warning':
      case 'medium':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Aviso</Badge>
      case 'info':
      case 'low':
        return <Badge variant="default" className="bg-blue-100 text-blue-800">Info</Badge>
      default:
        return <Badge variant="outline">Desconhecido</Badge>
    }
  }

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'LOGIN':
      case 'LOGOUT':
        return <User className="h-4 w-4" />
      case 'CREATE':
      case 'UPDATE':
      case 'DELETE':
        return <Database className="h-4 w-4" />
      case 'BACKUP':
        return <Settings className="h-4 w-4" />
      default:
        return <FileText className="h-4 w-4" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="destructive">Ativo</Badge>
      case 'resolved':
        return <Badge variant="default" className="bg-green-100 text-green-800">Resolvido</Badge>
      case 'investigating':
        return <Badge variant="secondary">Investigando</Badge>
      case 'completed':
        return <Badge variant="default" className="bg-green-100 text-green-800">Concluído</Badge>
      default:
        return <Badge variant="outline">Desconhecido</Badge>
    }
  }

  const exportLogs = () => {
    toast({
      title: "Exportação iniciada",
      description: "Os logs de auditoria estão sendo exportados.",
    })
  }

  const generateReport = () => {
    toast({
      title: "Relatório gerado",
      description: "Um novo relatório de compliance foi gerado.",
    })
  }

  const filteredLogs = auditLogs.filter(log => {
    const matchesSearch = log.user.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         log.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         log.details.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesAction = actionFilter === 'all' || log.action === actionFilter
    const matchesSeverity = severityFilter === 'all' || log.severity === severityFilter
    return matchesSearch && matchesAction && matchesSeverity
  })

  return (
    <DashboardLayout title="Auditoria">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <FileText className="h-6 w-6 text-primary" />
            <h2 className="text-2xl font-bold">Auditoria e Logs</h2>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={exportLogs} className="flex items-center gap-2">
              <Download className="h-4 w-4" />
              Exportar Logs
            </Button>
            <Button onClick={generateReport} className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Gerar Relatório
            </Button>
          </div>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <GlowCard customSize className="h-auto">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total de Eventos</p>
                <p className="text-2xl font-bold">{auditLogs.length}</p>
                <p className="text-sm text-muted-foreground">Últimas 24h</p>
              </div>
              <FileText className="h-8 w-8 text-blue-600" />
            </div>
          </GlowCard>

          <GlowCard customSize className="h-auto" glowColor="red">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Eventos Críticos</p>
                <p className="text-2xl font-bold">{auditLogs.filter(log => log.severity === 'critical').length}</p>
                <p className="text-sm text-red-600">Requer atenção</p>
              </div>
              <Shield className="h-8 w-8 text-red-600" />
            </div>
          </GlowCard>

          <GlowCard customSize className="h-auto" glowColor="green">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Usuários Ativos</p>
                <p className="text-2xl font-bold">{new Set(auditLogs.map(log => log.user)).size}</p>
                <p className="text-sm text-muted-foreground">Únicos</p>
              </div>
              <User className="h-8 w-8 text-green-600" />
            </div>
          </GlowCard>

          <GlowCard customSize className="h-auto" glowColor="purple">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">IPs Únicos</p>
                <p className="text-2xl font-bold">{new Set(auditLogs.map(log => log.ip)).size}</p>
                <p className="text-sm text-muted-foreground">Endereços</p>
              </div>
              <Database className="h-8 w-8 text-purple-600" />
            </div>
          </GlowCard>
        </div>

        <Tabs defaultValue="logs" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="logs">Logs de Auditoria</TabsTrigger>
            <TabsTrigger value="security">Eventos de Segurança</TabsTrigger>
            <TabsTrigger value="compliance">Compliance</TabsTrigger>
          </TabsList>

          <TabsContent value="logs" className="space-y-6">
            {/* Filters */}
            <Card>
              <CardHeader>
                <CardTitle>Filtros</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Buscar logs..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                  
                  <Select value={actionFilter} onValueChange={setActionFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="Filtrar por ação" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Todas as ações</SelectItem>
                      <SelectItem value="LOGIN">Login</SelectItem>
                      <SelectItem value="CREATE">Criar</SelectItem>
                      <SelectItem value="UPDATE">Atualizar</SelectItem>
                      <SelectItem value="DELETE">Excluir</SelectItem>
                      <SelectItem value="BACKUP">Backup</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={severityFilter} onValueChange={setSeverityFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="Filtrar por severidade" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Todas as severidades</SelectItem>
                      <SelectItem value="critical">Crítico</SelectItem>
                      <SelectItem value="warning">Aviso</SelectItem>
                      <SelectItem value="info">Informação</SelectItem>
                    </SelectContent>
                  </Select>

                  <Input type="date" placeholder="Data inicial" />
                </div>
              </CardContent>
            </Card>

            {/* Audit Logs Table */}
            <Card>
              <CardHeader>
                <CardTitle>Logs de Auditoria ({filteredLogs.length})</CardTitle>
                <CardDescription>
                  Registro detalhado de todas as atividades do sistema
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Data/Hora</TableHead>
                      <TableHead>Usuário</TableHead>
                      <TableHead>Ação</TableHead>
                      <TableHead>Recurso</TableHead>
                      <TableHead>Detalhes</TableHead>
                      <TableHead>IP</TableHead>
                      <TableHead>Severidade</TableHead>
                      <TableHead>Ações</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredLogs.map((log) => (
                      <TableRow key={log.id}>
                        <TableCell className="font-medium">
                          {new Date(log.timestamp).toLocaleString('pt-BR')}
                        </TableCell>
                        <TableCell>{log.user}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {getActionIcon(log.action)}
                            <Badge variant="outline">{log.action}</Badge>
                          </div>
                        </TableCell>
                        <TableCell>{log.resource}</TableCell>
                        <TableCell className="max-w-xs truncate" title={log.details}>
                          {log.details}
                        </TableCell>
                        <TableCell>{log.ip}</TableCell>
                        <TableCell>
                          {getSeverityBadge(log.severity)}
                        </TableCell>
                        <TableCell>
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="security" className="space-y-6">
            {/* Security Events */}
            <Card>
              <CardHeader>
                <CardTitle>Eventos de Segurança</CardTitle>
                <CardDescription>
                  Eventos relacionados à segurança do sistema
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Data/Hora</TableHead>
                      <TableHead>Tipo</TableHead>
                      <TableHead>Descrição</TableHead>
                      <TableHead>IP</TableHead>
                      <TableHead>Severidade</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Ações</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {securityEvents.map((event) => (
                      <TableRow key={event.id}>
                        <TableCell className="font-medium">
                          {new Date(event.timestamp).toLocaleString('pt-BR')}
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{event.type}</Badge>
                        </TableCell>
                        <TableCell>{event.description}</TableCell>
                        <TableCell>{event.ip}</TableCell>
                        <TableCell>
                          {getSeverityBadge(event.severity)}
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(event.status)}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Button variant="outline" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                            {event.status === 'active' && (
                              <Button variant="outline" size="sm">
                                Resolver
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="compliance" className="space-y-6">
            {/* Compliance Reports */}
            <Card>
              <CardHeader>
                <CardTitle>Relatórios de Compliance</CardTitle>
                <CardDescription>
                  Relatórios gerados para conformidade regulatória
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <h4 className="font-medium">Relatórios Disponíveis</h4>
                    <Button onClick={generateReport}>
                      Gerar Novo Relatório
                    </Button>
                  </div>
                  
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Nome</TableHead>
                        <TableHead>Tipo</TableHead>
                        <TableHead>Período</TableHead>
                        <TableHead>Gerado em</TableHead>
                        <TableHead>Tamanho</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Ações</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {complianceReports.map((report) => (
                        <TableRow key={report.id}>
                          <TableCell className="font-medium">{report.name}</TableCell>
                          <TableCell>
                            <Badge variant="outline">{report.type}</Badge>
                          </TableCell>
                          <TableCell>{report.period}</TableCell>
                          <TableCell>
                            {new Date(report.generatedAt).toLocaleString('pt-BR')}
                          </TableCell>
                          <TableCell>{report.size}</TableCell>
                          <TableCell>
                            {getStatusBadge(report.status)}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Button variant="outline" size="sm">
                                <Download className="h-4 w-4" />
                              </Button>
                              <Button variant="outline" size="sm">
                                <Eye className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}