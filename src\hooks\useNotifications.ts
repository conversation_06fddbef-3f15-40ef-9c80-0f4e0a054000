import { useState, useEffect } from 'react'
import { supabase } from '@/integrations/supabase/client'

export interface Notification {
  id: number
  title: string
  description: string
  time: string
  type: 'success' | 'info' | 'warning' | 'error'
  read: boolean
  userId?: string
  createdAt?: string
}

export function useNotifications() {
  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: 1,
      title: "Nova consulta IA concluída",
      description: "Análise jurisprudencial sobre direito trabalhista finalizada",
      time: "2 min atrás",
      type: "success",
      read: false
    },
    {
      id: 2,
      title: "Documento analisado",
      description: "Contrato de prestação de serviços foi processado",
      time: "15 min atrás",
      type: "info",
      read: false
    },
    {
      id: 3,
      title: "Prazo se aproximando",
      description: "Audiência marcada para amanhã às 14h",
      time: "1 hora atrás",
      type: "warning",
      read: true
    },
    {
      id: 4,
      title: "<PERSON>stema atualizado",
      description: "Nova versão da IA jurídica disponível",
      time: "3 horas atrás",
      type: "info",
      read: true
    },
    {
      id: 5,
      title: "Backup realizado",
      description: "Backup automático dos dados concluído com sucesso",
      time: "6 horas atrás",
      type: "success",
      read: true
    }
  ])

  const [loading, setLoading] = useState(false)

  const unreadCount = notifications.filter(n => !n.read).length

  const markAsRead = (notificationId: number) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === notificationId 
          ? { ...notification, read: true }
          : notification
      )
    )
  }

  const markAllAsRead = () => {
    setNotifications(prev => 
      prev.map(notification => ({ ...notification, read: true }))
    )
  }

  const addNotification = (notification: Omit<Notification, 'id'>) => {
    const newNotification: Notification = {
      ...notification,
      id: Date.now(), // Simple ID generation
    }
    setNotifications(prev => [newNotification, ...prev])
  }

  const removeNotification = (notificationId: number) => {
    setNotifications(prev => 
      prev.filter(notification => notification.id !== notificationId)
    )
  }

  const clearAllNotifications = () => {
    setNotifications([])
  }

  // Simular notificações em tempo real
  useEffect(() => {
    const interval = setInterval(() => {
      // Simular uma nova notificação ocasionalmente
      if (Math.random() < 0.1) { // 10% de chance a cada 30 segundos
        const randomNotifications = [
          {
            title: "Nova mensagem",
            description: "Você recebeu uma nova mensagem de um cliente",
            time: "agora",
            type: "info" as const,
            read: false
          },
          {
            title: "Lembrete de prazo",
            description: "Prazo processual se aproxima em 2 dias",
            time: "agora",
            type: "warning" as const,
            read: false
          },
          {
            title: "Análise concluída",
            description: "Análise de documento foi finalizada",
            time: "agora",
            type: "success" as const,
            read: false
          }
        ]
        
        const randomNotification = randomNotifications[Math.floor(Math.random() * randomNotifications.length)]
        addNotification(randomNotification)
      }
    }, 30000) // A cada 30 segundos

    return () => clearInterval(interval)
  }, [])

  // Função para buscar notificações do servidor (implementação futura)
  const fetchNotifications = async () => {
    try {
      setLoading(true)
      // Aqui você implementaria a busca real das notificações do Supabase
      // const { data, error } = await supabase
      //   .from('notifications')
      //   .select('*')
      //   .order('created_at', { ascending: false })
      
      // if (error) throw error
      // setNotifications(data || [])
    } catch (error) {
      console.error('Erro ao buscar notificações:', error)
    } finally {
      setLoading(false)
    }
  }

  // Função para marcar como lida no servidor (implementação futura)
  const markAsReadOnServer = async (notificationId: number) => {
    try {
      // Aqui você implementaria a atualização no Supabase
      // const { error } = await supabase
      //   .from('notifications')
      //   .update({ read: true })
      //   .eq('id', notificationId)
      
      // if (error) throw error
      markAsRead(notificationId)
    } catch (error) {
      console.error('Erro ao marcar notificação como lida:', error)
    }
  }

  return {
    notifications,
    unreadCount,
    loading,
    markAsRead,
    markAllAsRead,
    addNotification,
    removeNotification,
    clearAllNotifications,
    fetchNotifications,
    markAsReadOnServer
  }
}
