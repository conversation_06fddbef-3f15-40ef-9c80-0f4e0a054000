# <PERSON>ris Glow Hub

## Project info

Sistema jurídico com IA especializada para escritórios de advocacia.

## Como editar este código?

**Use seu IDE preferido**

Clone este repositório e faça as alterações localmente.

Requisitos: Node.js & npm instalados - [instalar com nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Siga estes passos:

```sh
# Passo 1: Clone o repositório
git clone <URL_DO_SEU_GIT>

# Passo 2: Navegue para o diretório do projeto
cd <NOME_DO_SEU_PROJETO>

# Passo 3: Configure as variáveis de ambiente
cp .env.example .env
# Edite o arquivo .env com suas credenciais do Supabase

# Passo 4: Instale as dependências necessárias
npm i

# Passo 5: Inicie o servidor de desenvolvimento
npm run dev
```

## Quais tecnologias são usadas neste projeto?

Este projeto foi construído com:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS
- Supabase

## Configuração do Supabase

1. Crie um projeto no [Supabase](https://supabase.com)
2. Copie o arquivo `.env.example` para `.env`:
   ```bash
   cp .env.example .env
   ```
3. No dashboard do Supabase, vá em Settings > API
4. Copie a URL do projeto e a chave pública (anon key)
5. Cole os valores no arquivo `.env`:
   ```env
   VITE_SUPABASE_URL=https://seu-projeto.supabase.co
   VITE_SUPABASE_ANON_KEY=sua_anon_key_aqui
   ```

## Como fazer deploy deste projeto?

Você pode fazer deploy usando qualquer plataforma de hospedagem que suporte aplicações React, como:

- Vercel
- Netlify
- GitHub Pages
- Heroku
