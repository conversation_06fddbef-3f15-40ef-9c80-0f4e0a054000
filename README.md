# <PERSON><PERSON> Glow Hub

## Project info

Sistema jurídico com IA especializada para escritórios de advocacia.

## Como editar este código?

**Use seu IDE preferido**

Clone este repositório e faça as alterações localmente.

Requisitos: Node.js & npm instalados - [instalar com nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Siga estes passos:

```sh
# Passo 1: Clone o repositório
git clone <URL_DO_SEU_GIT>

# Passo 2: Navegue para o diretório do projeto
cd <NOME_DO_SEU_PROJETO>

# Passo 3: Instale as dependências necessárias
npm i

# Passo 4: Inicie o servidor de desenvolvimento
npm run dev
```

## Quais tecnologias são usadas neste projeto?

Este projeto foi construído com:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS
- Supabase

## Como fazer deploy deste projeto?

Você pode fazer deploy usando qualquer plataforma de hospedagem que suporte aplicações React, como:

- Vercel
- Netlify
- GitHub Pages
- Heroku
