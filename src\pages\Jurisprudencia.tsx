import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { GlowCard } from "@/components/ui/glow-card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Search, BookOpen, Filter } from "lucide-react"

export default function Jurisprudencia() {
  return (
    <DashboardLayout title="Jurisprudência">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Pesquisa de Jurisprudência</h2>
            <p className="text-muted-foreground">
              Pesquise e analise decisões judiciais
            </p>
          </div>
        </div>

        <GlowCard glowColor="blue" customSize className="w-full">
          <div className="p-6">
            <CardTitle className="flex items-center mb-2">
              <Search className="mr-2 h-4 w-4" />
              Busca Avançada
            </CardTitle>
            <CardDescription className="mb-4">
              Digite os termos para pesquisar na jurisprudência
            </CardDescription>
            <div className="flex space-x-2">
              <div className="relative flex-1">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input placeholder="Digite sua consulta..." className="pl-8" />
              </div>
              <Button variant="outline">
                <Filter className="mr-2 h-4 w-4" />
                Filtros
              </Button>
              <Button>Pesquisar</Button>
            </div>
          </div>
        </GlowCard>

        <div className="grid gap-4">
          <GlowCard glowColor="green" customSize className="w-full">
            <div className="p-6">
              <CardTitle className="flex items-center mb-2">
                <BookOpen className="mr-2 h-4 w-4" />
                Resultados Recentes
              </CardTitle>
              <CardDescription className="mb-4">
                Suas últimas pesquisas
              </CardDescription>
              <p className="text-muted-foreground">
                Os resultados de suas pesquisas aparecerão aqui.
              </p>
            </div>
          </GlowCard>
        </div>
      </div>
    </DashboardLayout>
  )
}