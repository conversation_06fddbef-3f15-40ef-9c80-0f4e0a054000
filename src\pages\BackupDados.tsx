import { useState } from 'react'
import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { GlowCard } from '@/components/ui/glow-card'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogT<PERSON>le, AlertDialogTrigger } from '@/components/ui/alert-dialog'
import { Database, Download, Upload, Settings, CheckCircle, AlertTriangle, Clock, HardDrive } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

const backupHistory = [
  {
    id: '1',
    type: 'automatic',
    date: '2024-01-15 02:00:00',
    size: '2.4 GB',
    status: 'completed',
    duration: '4m 32s',
    tables: 15,
    records: 125843
  },
  {
    id: '2', 
    type: 'manual',
    date: '2024-01-14 16:45:00',
    size: '2.3 GB',
    status: 'completed',
    duration: '3m 58s',
    tables: 15,
    records: 124521
  },
  {
    id: '3',
    type: 'automatic',
    date: '2024-01-14 02:00:00',
    size: '2.3 GB',
    status: 'completed',
    duration: '4m 12s',
    tables: 15,
    records: 123876
  },
  {
    id: '4',
    type: 'automatic',
    date: '2024-01-13 02:00:00',
    size: '2.2 GB',
    status: 'failed',
    duration: '0m 45s',
    tables: 0,
    records: 0
  },
  {
    id: '5',
    type: 'automatic',
    date: '2024-01-12 02:00:00',
    size: '2.2 GB',
    status: 'completed',
    duration: '4m 05s',
    tables: 15,
    records: 122340
  }
]

const databaseTables = [
  { name: 'users', records: 1247, size: '45.2 MB', lastUpdate: '2024-01-15 14:30:00' },
  { name: 'organizations', records: 89, size: '12.8 MB', lastUpdate: '2024-01-15 14:25:00' },
  { name: 'documents', records: 5643, size: '1.2 GB', lastUpdate: '2024-01-15 14:20:00' },
  { name: 'cases', records: 2341, size: '256.7 MB', lastUpdate: '2024-01-15 14:15:00' },
  { name: 'clients', records: 3421, size: '89.4 MB', lastUpdate: '2024-01-15 14:10:00' },
  { name: 'lawyers', records: 456, size: '23.1 MB', lastUpdate: '2024-01-15 14:05:00' }
]

export default function BackupDados() {
  const [isBackupRunning, setIsBackupRunning] = useState(false)
  const [backupProgress, setBackupProgress] = useState(0)
  const [selectedTables, setSelectedTables] = useState<string[]>([])
  const [backupFrequency, setBackupFrequency] = useState('daily')
  const [retentionDays, setRetentionDays] = useState(30)
  const { toast } = useToast()

  const startManualBackup = async () => {
    setIsBackupRunning(true)
    setBackupProgress(0)

    // Simular progresso do backup
    const interval = setInterval(() => {
      setBackupProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval)
          setIsBackupRunning(false)
          toast({
            title: "Backup concluído",
            description: "O backup manual foi realizado com sucesso.",
          })
          return 100
        }
        return prev + 10
      })
    }, 500)
  }

  const downloadBackup = async (backupId: string) => {
    try {
      // Simular download - em produção, isso seria um link para o arquivo
      toast({
        title: "Download iniciado",
        description: "O download do backup foi iniciado.",
      })
      
      // Aqui você implementaria a lógica real de download
      // window.open(`/api/backups/${backupId}/download`, '_blank')
    } catch (error) {
      toast({
        title: "Erro no download",
        description: "Não foi possível baixar o backup.",
        variant: "destructive",
      })
    }
  }

  const restoreBackup = async (backupId: string) => {
    try {
      // Aqui você implementaria a lógica de restauração
      toast({
        title: "Restauração iniciada",
        description: "A restauração do backup foi iniciada. Isso pode levar alguns minutos.",
      })
    } catch (error) {
      toast({
        title: "Erro na restauração",
        description: "Não foi possível restaurar o backup.",
        variant: "destructive",
      })
    }
  }

  const deleteBackup = async (backupId: string) => {
    try {
      // Aqui você implementaria a lógica de exclusão
      toast({
        title: "Backup excluído",
        description: "O backup foi excluído com sucesso.",
      })
    } catch (error) {
      toast({
        title: "Erro na exclusão",
        description: "Não foi possível excluir o backup.",
        variant: "destructive",
      })
    }
  }

  const createIndividualBackup = async (tableName: string) => {
    try {
      toast({
        title: "Backup individual iniciado",
        description: `Criando backup da tabela ${tableName}...`,
      })
    } catch (error) {
      toast({
        title: "Erro no backup individual",
        description: "Não foi possível criar o backup da tabela.",
        variant: "destructive",
      })
    }
  }

  const optimizeTable = async (tableName: string) => {
    try {
      toast({
        title: "Otimização iniciada",
        description: `Otimizando tabela ${tableName}...`,
      })
    } catch (error) {
      toast({
        title: "Erro na otimização",
        description: "Não foi possível otimizar a tabela.",
        variant: "destructive",
      })
    }
  }

  const saveBackupSettings = async () => {
    try {
      toast({
        title: "Configurações salvas",
        description: "As configurações de backup foram atualizadas.",
      })
    } catch (error) {
      toast({
        title: "Erro",
        description: "Não foi possível salvar as configurações.",
        variant: "destructive",
      })
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge variant="default" className="bg-green-100 text-green-800">Concluído</Badge>
      case 'failed':
        return <Badge variant="destructive">Falhou</Badge>
      case 'running':
        return <Badge variant="secondary">Em andamento</Badge>
      default:
        return <Badge variant="outline">Desconhecido</Badge>
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'failed':
        return <AlertTriangle className="h-4 w-4 text-red-600" />
      case 'running':
        return <Clock className="h-4 w-4 text-blue-600" />
      default:
        return <Clock className="h-4 w-4 text-gray-400" />
    }
  }

  const totalSize = backupHistory
    .filter(backup => backup.status === 'completed')
    .reduce((total, backup) => total + parseFloat(backup.size.replace(' GB', '')), 0)

  const successRate = (backupHistory.filter(b => b.status === 'completed').length / backupHistory.length) * 100

  return (
    <DashboardLayout title="Backup & Dados">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Database className="h-6 w-6 text-primary" />
            <h2 className="text-2xl font-bold">Backup & Dados</h2>
          </div>
          <Button 
            onClick={startManualBackup} 
            disabled={isBackupRunning}
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            {isBackupRunning ? 'Criando Backup...' : 'Backup Manual'}
          </Button>
        </div>

        {/* Backup Status */}
        {isBackupRunning && (
          <Card>
            <CardHeader>
              <CardTitle>Backup em Andamento</CardTitle>
              <CardDescription>
                Criando backup manual do banco de dados...
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Progresso</span>
                  <span>{backupProgress}%</span>
                </div>
                <Progress value={backupProgress} className="w-full" />
              </div>
            </CardContent>
          </Card>
        )}

        {/* Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <GlowCard customSize className="h-auto">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total de Backups</p>
                <p className="text-2xl font-bold">{backupHistory.length}</p>
              </div>
              <Database className="h-8 w-8 text-blue-600" />
            </div>
          </GlowCard>

          <GlowCard customSize className="h-auto" glowColor="green">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Taxa de Sucesso</p>
                <p className="text-2xl font-bold">{successRate.toFixed(1)}%</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </GlowCard>

          <GlowCard customSize className="h-auto" glowColor="purple">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Espaço Utilizado</p>
                <p className="text-2xl font-bold">{totalSize.toFixed(1)} GB</p>
              </div>
              <HardDrive className="h-8 w-8 text-purple-600" />
            </div>
          </GlowCard>

          <GlowCard customSize className="h-auto" glowColor="orange">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Último Backup</p>
                <p className="text-2xl font-bold">Hoje</p>
                <p className="text-xs text-muted-foreground">02:00</p>
              </div>
              <Clock className="h-8 w-8 text-orange-600" />
            </div>
          </GlowCard>
        </div>

        <Tabs defaultValue="history" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="history">Histórico</TabsTrigger>
            <TabsTrigger value="schedule">Agendamento</TabsTrigger>
            <TabsTrigger value="tables">Tabelas</TabsTrigger>
            <TabsTrigger value="settings">Configurações</TabsTrigger>
          </TabsList>

          <TabsContent value="history" className="space-y-6">
            {/* Backup History */}
            <Card>
              <CardHeader>
                <CardTitle>Histórico de Backups</CardTitle>
                <CardDescription>
                  Lista de todos os backups realizados
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Data/Hora</TableHead>
                      <TableHead>Tipo</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Tamanho</TableHead>
                      <TableHead>Duração</TableHead>
                      <TableHead>Registros</TableHead>
                      <TableHead>Ações</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {backupHistory.map((backup) => (
                      <TableRow key={backup.id}>
                        <TableCell className="font-medium">
                          {new Date(backup.date).toLocaleString('pt-BR')}
                        </TableCell>
                        <TableCell>
                          <Badge variant={backup.type === 'automatic' ? 'outline' : 'secondary'}>
                            {backup.type === 'automatic' ? 'Automático' : 'Manual'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {getStatusIcon(backup.status)}
                            {getStatusBadge(backup.status)}
                          </div>
                        </TableCell>
                        <TableCell>{backup.size}</TableCell>
                        <TableCell>{backup.duration}</TableCell>
                        <TableCell>{backup.records.toLocaleString('pt-BR')}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {backup.status === 'completed' && (
                              <>
                                <Button 
                                  variant="outline" 
                                  size="sm"
                                  onClick={() => downloadBackup(backup.id)}
                                >
                                  <Download className="h-4 w-4" />
                                </Button>
                                
                                <AlertDialog>
                                  <AlertDialogTrigger asChild>
                                    <Button variant="outline" size="sm">
                                      <Upload className="h-4 w-4" />
                                    </Button>
                                  </AlertDialogTrigger>
                                  <AlertDialogContent>
                                    <AlertDialogHeader>
                                      <AlertDialogTitle>Confirmar Restauração</AlertDialogTitle>
                                      <AlertDialogDescription>
                                        Tem certeza que deseja restaurar este backup? Todos os dados atuais serão substituídos. Esta ação não pode ser desfeita.
                                      </AlertDialogDescription>
                                    </AlertDialogHeader>
                                    <AlertDialogFooter>
                                      <AlertDialogCancel>Cancelar</AlertDialogCancel>
                                      <AlertDialogAction 
                                        onClick={() => restoreBackup(backup.id)}
                                        className="bg-destructive text-destructive-foreground"
                                      >
                                        Restaurar
                                      </AlertDialogAction>
                                    </AlertDialogFooter>
                                  </AlertDialogContent>
                                </AlertDialog>
                              </>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="schedule" className="space-y-6">
            {/* Backup Schedule */}
            <Card>
              <CardHeader>
                <CardTitle>Agendamento de Backups</CardTitle>
                <CardDescription>
                  Configure quando os backups automáticos devem ser executados
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="frequency">Frequência</Label>
                    <Select value={backupFrequency} onValueChange={setBackupFrequency}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="hourly">A cada hora</SelectItem>
                        <SelectItem value="daily">Diário</SelectItem>
                        <SelectItem value="weekly">Semanal</SelectItem>
                        <SelectItem value="monthly">Mensal</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label htmlFor="retention">Retenção (dias)</Label>
                    <Input
                      id="retention"
                      type="number"
                      value={retentionDays}
                      onChange={(e) => setRetentionDays(Number(e.target.value))}
                      min="1"
                      max="365"
                    />
                  </div>
                </div>

                <div className="border-t pt-4">
                  <h4 className="font-medium mb-3">Próximos Backups Agendados</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between items-center p-3 bg-muted rounded-lg">
                      <span>Backup Automático</span>
                      <span className="text-sm text-muted-foreground">Amanhã às 02:00</span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-muted rounded-lg">
                      <span>Backup Semanal</span>
                      <span className="text-sm text-muted-foreground">Domingo às 01:00</span>
                    </div>
                  </div>
                </div>

                <Button onClick={saveBackupSettings}>Salvar Configurações</Button>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="tables" className="space-y-6">
            {/* Database Tables */}
            <Card>
              <CardHeader>
                <CardTitle>Tabelas do Banco de Dados</CardTitle>
                <CardDescription>
                  Visualize informações sobre as tabelas do banco de dados
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Tabela</TableHead>
                      <TableHead>Registros</TableHead>
                      <TableHead>Tamanho</TableHead>
                      <TableHead>Última Atualização</TableHead>
                      <TableHead>Ações</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {databaseTables.map((table) => (
                      <TableRow key={table.name}>
                        <TableCell className="font-medium">{table.name}</TableCell>
                        <TableCell>{table.records.toLocaleString('pt-BR')}</TableCell>
                        <TableCell>{table.size}</TableCell>
                        <TableCell>
                          {new Date(table.lastUpdate).toLocaleString('pt-BR')}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => createIndividualBackup(table.name)}
                            >
                              Backup Individual
                            </Button>
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => optimizeTable(table.name)}
                            >
                              Otimizar
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings" className="space-y-6">
            {/* Backup Settings */}
            <Card>
              <CardHeader>
                <CardTitle>Configurações de Backup</CardTitle>
                <CardDescription>
                  Configure as opções avançadas de backup
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <div>
                    <Label>Compressão</Label>
                    <Select defaultValue="gzip">
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">Sem compressão</SelectItem>
                        <SelectItem value="gzip">GZIP</SelectItem>
                        <SelectItem value="bzip2">BZIP2</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="maxSize">Tamanho Máximo do Backup (GB)</Label>
                    <Input
                      id="maxSize"
                      type="number"
                      defaultValue="5"
                      min="1"
                      max="100"
                    />
                  </div>

                  <div>
                    <Label htmlFor="storage">Local de Armazenamento</Label>
                    <Select defaultValue="local">
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="local">Armazenamento Local</SelectItem>
                        <SelectItem value="s3">Amazon S3</SelectItem>
                        <SelectItem value="gcs">Google Cloud Storage</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="encryption">Criptografia</Label>
                    <Select defaultValue="aes256">
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">Sem criptografia</SelectItem>
                        <SelectItem value="aes256">AES-256</SelectItem>
                        <SelectItem value="pgp">PGP</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="border-t pt-4">
                  <Button onClick={saveBackupSettings}>Salvar Configurações</Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}