import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { GlowCard } from '@/components/ui/glow-card'
import { 
  Users, 
  Building, 
  Shield, 
  Settings, 
  Crown, 
  UserPlus,
  DollarSign,
  Activity,
  Database,
  FileText
} from 'lucide-react'
import { supabase } from '@/integrations/supabase/client'
import { useToast } from '@/hooks/use-toast'

interface OwnerStats {
  totalUsers: number
  totalOrganizations: number
  activeSubscriptions: number
  systemHealth: 'good' | 'warning' | 'critical'
}

export function OwnerDashboard() {
  const [stats, setStats] = useState<OwnerStats>({
    totalUsers: 0,
    totalOrganizations: 0,
    activeSubscriptions: 0,
    systemHealth: 'good'
  })
  const [loading, setLoading] = useState(true)
  const { toast } = useToast()

  useEffect(() => {
    fetchOwnerStats()
  }, [])

  const fetchOwnerStats = async () => {
    try {
      // Fetch total users count
      const { count: usersCount } = await supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true })

      // Fetch total organizations count
      const { count: orgsCount } = await supabase
        .from('organizations')
        .select('*', { count: 'exact', head: true })

      setStats({
        totalUsers: usersCount || 0,
        totalOrganizations: orgsCount || 0,
        activeSubscriptions: orgsCount || 0, // Using orgs count as subscription count for now
        systemHealth: 'good'
      })
    } catch (error) {
      console.error('Error fetching owner stats:', error)
      toast({
        title: "Erro",
        description: "Não foi possível carregar as estatísticas do sistema.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const ownerActions = [
    {
      title: "Gerenciar Usuários",
      description: "Visualizar e gerenciar todos os usuários do sistema",
      icon: Users,
      action: () => {
        toast({
          title: "Funcionalidade em desenvolvimento",
          description: "O gerenciamento de usuários estará disponível em breve.",
        })
      }
    },
    {
      title: "Organizações",
      description: "Gerenciar organizações e seus membros",
      icon: Building,
      action: () => {
        toast({
          title: "Funcionalidade em desenvolvimento", 
          description: "O gerenciamento de organizações estará disponível em breve.",
        })
      }
    },
    {
      title: "Permissões Globais",
      description: "Configurar roles e permissões do sistema",
      icon: Shield,
      action: () => {
        toast({
          title: "Funcionalidade em desenvolvimento",
          description: "O gerenciamento de permissões estará disponível em breve.",
        })
      }
    },
    {
      title: "Configurações do Sistema",
      description: "Configurações avançadas e manutenção",
      icon: Settings,
      action: () => {
        toast({
          title: "Funcionalidade em desenvolvimento",
          description: "As configurações do sistema estarão disponíveis em breve.",
        })
      }
    },
    {
      title: "Relatórios Financeiros",
      description: "Visualizar receitas e estatísticas de pagamento",
      icon: DollarSign,
      action: () => {
        toast({
          title: "Funcionalidade em desenvolvimento",
          description: "Os relatórios financeiros estarão disponíveis em breve.",
        })
      }
    },
    {
      title: "Monitoramento",
      description: "Status do sistema e métricas de performance",
      icon: Activity,
      action: () => {
        toast({
          title: "Funcionalidade em desenvolvimento",
          description: "O monitoramento do sistema estará disponível em breve.",
        })
      }
    },
    {
      title: "Backup & Dados",
      description: "Gerenciar backups e integridade dos dados",
      icon: Database,
      action: () => {
        toast({
          title: "Funcionalidade em desenvolvimento",
          description: "O gerenciamento de backups estará disponível em breve.",
        })
      }
    },
    {
      title: "Auditoria",
      description: "Logs de atividades e auditoria do sistema",
      icon: FileText,
      action: () => {
        toast({
          title: "Funcionalidade em desenvolvimento",
          description: "Os logs de auditoria estarão disponíveis em breve.",
        })
      }
    }
  ]

  const getHealthBadgeVariant = (health: string) => {
    switch (health) {
      case 'good': return 'default'
      case 'warning': return 'secondary'
      case 'critical': return 'destructive'
      default: return 'default'
    }
  }

  const getHealthText = (health: string) => {
    switch (health) {
      case 'good': return 'Sistema Operacional'
      case 'warning': return 'Atenção Necessária'
      case 'critical': return 'Sistema Crítico'
      default: return 'Status Desconhecido'
    }
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="flex items-center gap-2 mb-6">
          <Crown className="h-6 w-6 text-warning" />
          <h2 className="text-2xl font-bold text-foreground">Painel do Proprietário</h2>
        </div>
        <div className="animate-pulse space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-muted rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-2">
        <Crown className="h-6 w-6 text-warning" />
        <h2 className="text-2xl font-bold text-foreground">Painel do Proprietário</h2>
        <Badge variant="secondary" className="ml-2">
          Acesso Exclusivo
        </Badge>
      </div>

      {/* System Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <GlowCard glowColor="blue" customSize className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Total de Usuários</p>
              <p className="text-2xl font-bold text-foreground">{stats.totalUsers}</p>
            </div>
            <Users className="h-8 w-8 text-primary" />
          </div>
        </GlowCard>

        <GlowCard glowColor="green" customSize className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Organizações</p>
              <p className="text-2xl font-bold text-foreground">{stats.totalOrganizations}</p>
            </div>
            <Building className="h-8 w-8 text-primary" />
          </div>
        </GlowCard>

        <GlowCard glowColor="purple" customSize className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Assinaturas Ativas</p>
              <p className="text-2xl font-bold text-foreground">{stats.activeSubscriptions}</p>
            </div>
            <DollarSign className="h-8 w-8 text-primary" />
          </div>
        </GlowCard>

        <GlowCard glowColor="orange" customSize className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Status do Sistema</p>
              <Badge variant={getHealthBadgeVariant(stats.systemHealth)}>
                {getHealthText(stats.systemHealth)}
              </Badge>
            </div>
            <Activity className="h-8 w-8 text-primary" />
          </div>
        </GlowCard>
      </div>

      {/* Owner Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Ferramentas Administrativas
          </CardTitle>
          <CardDescription>
            Acesso exclusivo às funcionalidades de administração do sistema
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {ownerActions.map((action, index) => {
              const Icon = action.icon
              return (
                <Button
                  key={index}
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-start gap-2 hover:bg-muted/50"
                  onClick={action.action}
                >
                  <div className="flex items-center gap-2 w-full">
                    <Icon className="h-5 w-5 text-primary" />
                    <span className="font-medium text-sm">{action.title}</span>
                  </div>
                  <p className="text-xs text-muted-foreground text-left">
                    {action.description}
                  </p>
                </Button>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Ações Rápidas</CardTitle>
          <CardDescription>
            Ações frequentes para administração do sistema
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-3">
            <Button className="flex items-center gap-2">
              <UserPlus className="h-4 w-4" />
              Convidar Usuário
            </Button>
            <Button variant="outline" className="flex items-center gap-2">
              <Building className="h-4 w-4" />
              Nova Organização
            </Button>
            <Button variant="outline" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Relatório Geral
            </Button>
            <Button variant="outline" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Configurações
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}