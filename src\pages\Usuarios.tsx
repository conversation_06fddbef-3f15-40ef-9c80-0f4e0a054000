import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { GlowCard } from "@/components/ui/glow-card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Plus, Search, UserPlus, Settings, Shield } from "lucide-react"

export default function Usuarios() {
  return (
    <DashboardLayout title="Usuários">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Gestão de Usuários</h2>
            <p className="text-muted-foreground">
              Gerencie usuários e suas permissões
            </p>
          </div>
          <Button>
            <UserPlus className="mr-2 h-4 w-4" />
            Convidar <PERSON>
          </But<PERSON>>
        </div>

        <div className="flex items-center space-x-2">
          <div className="relative flex-1">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input placeholder="Buscar usuários..." className="pl-8" />
          </div>
          <Button variant="outline">
            <Shield className="mr-2 h-4 w-4" />
            Gerenciar Permissões
          </Button>
        </div>

        <div className="grid gap-4">
          {/* Usuário Owner */}
          <GlowCard glowColor="blue" customSize className="w-full">
            <div className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                    <span className="text-sm font-semibold text-primary-foreground">RN</span>
                  </div>
                  <div>
                    <CardTitle className="text-lg">Rodolpho Neto</CardTitle>
                    <CardDescription><EMAIL></CardDescription>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge variant="default">Owner</Badge>
                  <Button variant="outline" size="sm">
                    <Settings className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <p className="text-sm text-muted-foreground mt-4">
                Administrador principal do sistema com acesso total.
              </p>
            </div>
          </GlowCard>

          {/* Usuários de exemplo */}
          {[
            { name: "Maria Silva", email: "<EMAIL>", role: "Advogada", badge: "admin", color: "green" as const },
            { name: "João Santos", email: "<EMAIL>", role: "Estagiário", badge: "assistant", color: "purple" as const },
            { name: "Ana Costa", email: "<EMAIL>", role: "Cliente", badge: "client", color: "orange" as const }
          ].map((user, i) => (
            <GlowCard key={i} glowColor={user.color} customSize className="w-full">
              <div className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-muted rounded-full flex items-center justify-center">
                      <span className="text-sm font-semibold">{user.name.split(' ').map(n => n[0]).join('')}</span>
                    </div>
                    <div>
                      <CardTitle className="text-lg">{user.name}</CardTitle>
                      <CardDescription>{user.email}</CardDescription>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant={user.badge === 'admin' ? 'default' : 'secondary'}>
                      {user.badge === 'admin' ? 'Admin' : user.badge === 'assistant' ? 'Assistente' : 'Cliente'}
                    </Badge>
                    <Button variant="outline" size="sm">
                      <Settings className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <p className="text-sm text-muted-foreground mt-4">
                  {user.role} - Último acesso há 2 dias
                </p>
              </div>
            </GlowCard>
          ))}
        </div>
      </div>
    </DashboardLayout>
  )
}