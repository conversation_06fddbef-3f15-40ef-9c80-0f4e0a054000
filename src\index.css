@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 220 100% 50%;
    --primary-foreground: 0 0% 100%;
    --primary-glow: 220 100% 70%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 45 100% 70%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 220 100% 50%;

    --legal-blue: 220 100% 50%;
    --legal-gold: 45 100% 70%;
    --legal-navy: 220 100% 15%;
    --legal-light-blue: 220 100% 95%;
    
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-glow)));
    --gradient-legal: linear-gradient(135deg, hsl(var(--legal-blue)), hsl(var(--legal-gold)));
    --gradient-dark: linear-gradient(135deg, hsl(var(--legal-navy)), hsl(var(--primary)));
    
    --shadow-legal: 0 10px 30px -10px hsl(var(--primary) / 0.3);
    --shadow-glow: 0 0 40px hsl(var(--primary-glow) / 0.4);

    --radius: 0.75rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 220 20% 8%;
    --card-foreground: 210 40% 98%;

    --popover: 220 20% 8%;
    --popover-foreground: 210 40% 98%;

    --primary: 220 100% 60%;
    --primary-foreground: 0 0% 100%;
    --primary-glow: 220 100% 80%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 45 100% 60%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 62.8% 50.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 220 100% 60%;

    --legal-blue: 220 100% 60%;
    --legal-gold: 45 100% 60%;
    --legal-navy: 220 20% 8%;
    --legal-light-blue: 220 50% 20%;

    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-glow)));
    --gradient-legal: linear-gradient(135deg, hsl(var(--legal-blue)), hsl(var(--legal-gold)));
    --gradient-dark: linear-gradient(135deg, hsl(var(--legal-navy)), hsl(var(--primary)));
    
    --shadow-legal: 0 10px 30px -10px hsl(var(--primary) / 0.5);
    --shadow-glow: 0 0 40px hsl(var(--primary-glow) / 0.6);
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}