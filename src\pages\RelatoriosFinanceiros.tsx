import { useState } from 'react'
import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { GlowCard } from '@/components/ui/glow-card'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { DollarSign, TrendingUp, TrendingDown, Download, Calendar, CreditCard, PieChart, BarChart3 } from 'lucide-react'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, <PERSON>lt<PERSON>, Responsive<PERSON><PERSON><PERSON>, <PERSON><PERSON>hart as Recharts<PERSON>ie<PERSON>hart, Cell, BarChart, Bar, Pie } from 'recharts'

const revenueData = [
  { month: 'Jan', revenue: 12500, subscriptions: 25 },
  { month: 'Fev', revenue: 15200, subscriptions: 32 },
  { month: 'Mar', revenue: 18900, subscriptions: 41 },
  { month: 'Abr', revenue: 22100, subscriptions: 48 },
  { month: 'Mai', revenue: 19800, subscriptions: 45 },
  { month: 'Jun', revenue: 25600, subscriptions: 52 }
]

const subscriptionData = [
  { name: 'Básico', value: 120, color: '#8884d8' },
  { name: 'Professional', value: 80, color: '#82ca9d' },
  { name: 'Enterprise', value: 45, color: '#ffc658' }
]

const transactionData = [
  { id: '1', organization: 'Advocacia Silva & Santos', plan: 'Professional', amount: 299.90, date: '2024-01-15', status: 'paid' },
  { id: '2', organization: 'Escritório Jurídico Lima', plan: 'Enterprise', amount: 499.90, date: '2024-01-14', status: 'paid' },
  { id: '3', organization: 'Consultoria Legal SP', plan: 'Básico', amount: 99.90, date: '2024-01-13', status: 'pending' },
  { id: '4', organization: 'Advocacia Pereira', plan: 'Professional', amount: 299.90, date: '2024-01-12', status: 'paid' },
  { id: '5', organization: 'Jurídico & Associados', plan: 'Enterprise', amount: 499.90, date: '2024-01-11', status: 'failed' }
]

export default function RelatoriosFinanceiros() {
  const [dateRange, setDateRange] = useState('last30days')
  const [reportType, setReportType] = useState('revenue')

  const totalRevenue = revenueData.reduce((sum, item) => sum + item.revenue, 0)
  const totalSubscriptions = subscriptionData.reduce((sum, item) => sum + item.value, 0)
  const avgRevenuePerMonth = totalRevenue / revenueData.length

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'paid':
        return <Badge variant="default" className="bg-green-100 text-green-800">Pago</Badge>
      case 'pending':
        return <Badge variant="secondary">Pendente</Badge>
      case 'failed':
        return <Badge variant="destructive">Falhou</Badge>
      default:
        return <Badge variant="outline">Desconhecido</Badge>
    }
  }

  return (
    <DashboardLayout title="Relatórios Financeiros">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <DollarSign className="h-6 w-6 text-primary" />
            <h2 className="text-2xl font-bold">Relatórios Financeiros</h2>
          </div>
          <div className="flex items-center gap-2">
            <Select value={dateRange} onValueChange={setDateRange}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="last7days">Últimos 7 dias</SelectItem>
                <SelectItem value="last30days">Últimos 30 dias</SelectItem>
                <SelectItem value="last90days">Últimos 90 dias</SelectItem>
                <SelectItem value="last12months">Últimos 12 meses</SelectItem>
                <SelectItem value="custom">Período personalizado</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" className="flex items-center gap-2">
              <Download className="h-4 w-4" />
              Exportar
            </Button>
          </div>
        </div>

        {/* KPI Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <GlowCard customSize className="h-auto" glowColor="green">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Receita Total</p>
                <p className="text-2xl font-bold">R$ {totalRevenue.toLocaleString('pt-BR')}</p>
                <p className="text-sm text-green-600 flex items-center gap-1">
                  <TrendingUp className="h-3 w-3" />
                  +12.5% vs mês anterior
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-green-600" />
            </div>
          </GlowCard>

          <GlowCard customSize className="h-auto">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Assinaturas Ativas</p>
                <p className="text-2xl font-bold">{totalSubscriptions}</p>
                <p className="text-sm text-green-600 flex items-center gap-1">
                  <TrendingUp className="h-3 w-3" />
                  +8.2% vs mês anterior
                </p>
              </div>
              <CreditCard className="h-8 w-8 text-blue-600" />
            </div>
          </GlowCard>

          <GlowCard customSize className="h-auto" glowColor="purple">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Receita Média Mensal</p>
                <p className="text-2xl font-bold">R$ {avgRevenuePerMonth.toLocaleString('pt-BR', { maximumFractionDigits: 0 })}</p>
                <p className="text-sm text-green-600 flex items-center gap-1">
                  <TrendingUp className="h-3 w-3" />
                  +5.1% vs mês anterior
                </p>
              </div>
              <BarChart3 className="h-8 w-8 text-purple-600" />
            </div>
          </GlowCard>

          <GlowCard customSize className="h-auto" glowColor="orange">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Taxa de Churn</p>
                <p className="text-2xl font-bold">2.4%</p>
                <p className="text-sm text-red-600 flex items-center gap-1">
                  <TrendingDown className="h-3 w-3" />
                  +0.3% vs mês anterior
                </p>
              </div>
              <PieChart className="h-8 w-8 text-orange-600" />
            </div>
          </GlowCard>
        </div>

        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Visão Geral</TabsTrigger>
            <TabsTrigger value="subscriptions">Assinaturas</TabsTrigger>
            <TabsTrigger value="transactions">Transações</TabsTrigger>
            <TabsTrigger value="analytics">Análises</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* Revenue Chart */}
            <Card>
              <CardHeader>
                <CardTitle>Evolução da Receita</CardTitle>
                <CardDescription>
                  Receita mensal e número de assinaturas nos últimos 6 meses
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={revenueData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip 
                      formatter={(value, name) => [
                        name === 'revenue' ? `R$ ${value.toLocaleString('pt-BR')}` : value,
                        name === 'revenue' ? 'Receita' : 'Assinaturas'
                      ]}
                    />
                    <Line type="monotone" dataKey="revenue" stroke="#8884d8" strokeWidth={2} />
                    <Line type="monotone" dataKey="subscriptions" stroke="#82ca9d" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Subscription Distribution */}
              <Card>
                <CardHeader>
                  <CardTitle>Distribuição por Plano</CardTitle>
                  <CardDescription>
                    Número de assinaturas por plano
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={250}>
                    <RechartsPieChart>
                      <Pie
                        data={subscriptionData}
                        cx="50%"
                        cy="50%"
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      >
                        {subscriptionData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </RechartsPieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* Monthly Revenue by Plan */}
              <Card>
                <CardHeader>
                  <CardTitle>Receita por Plano</CardTitle>
                  <CardDescription>
                    Receita mensal distribuída por tipo de plano
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={250}>
                    <BarChart data={revenueData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <Tooltip formatter={(value) => `R$ ${value.toLocaleString('pt-BR')}`} />
                      <Bar dataKey="revenue" fill="#8884d8" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="subscriptions" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Análise de Assinaturas</CardTitle>
                <CardDescription>
                  Detalhamento das assinaturas ativas e métricas relacionadas
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                  {subscriptionData.map((plan) => (
                    <div key={plan.name} className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="font-medium">{plan.name}</h3>
                        <Badge style={{ backgroundColor: plan.color, color: 'white' }}>
                          {plan.value}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {((plan.value / totalSubscriptions) * 100).toFixed(1)}% do total
                      </p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="transactions" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Transações Recentes</CardTitle>
                <CardDescription>
                  Histórico de pagamentos e transações do sistema
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Organização</TableHead>
                      <TableHead>Plano</TableHead>
                      <TableHead>Valor</TableHead>
                      <TableHead>Data</TableHead>
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {transactionData.map((transaction) => (
                      <TableRow key={transaction.id}>
                        <TableCell className="font-medium">
                          {transaction.organization}
                        </TableCell>
                        <TableCell>{transaction.plan}</TableCell>
                        <TableCell>
                          R$ {transaction.amount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                        </TableCell>
                        <TableCell>
                          {new Date(transaction.date).toLocaleDateString('pt-BR')}
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(transaction.status)}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Métricas de Performance</CardTitle>
                  <CardDescription>
                    Indicadores chave de performance financeira
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between items-center p-3 bg-muted rounded-lg">
                    <span className="font-medium">LTV (Lifetime Value)</span>
                    <span className="text-xl font-bold">R$ 2.450</span>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-muted rounded-lg">
                    <span className="font-medium">CAC (Customer Acquisition Cost)</span>
                    <span className="text-xl font-bold">R$ 150</span>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-muted rounded-lg">
                    <span className="font-medium">Payback Period</span>
                    <span className="text-xl font-bold">3.2 meses</span>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-muted rounded-lg">
                    <span className="font-medium">MRR (Monthly Recurring Revenue)</span>
                    <span className="text-xl font-bold">R$ 25.600</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Projeções</CardTitle>
                  <CardDescription>
                    Projeções financeiras para os próximos meses
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Receita projetada (próximo mês)</span>
                      <span className="font-bold">R$ 28.400</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Novas assinaturas esperadas</span>
                      <span className="font-bold">15</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Churn esperado</span>
                      <span className="font-bold text-red-600">6</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Crescimento mensal</span>
                      <span className="font-bold text-green-600">+10.8%</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}