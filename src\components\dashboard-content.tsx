import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON>lowCard } from "@/components/ui/glow-card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { OwnerDashboard } from "@/components/owner-dashboard"
import { useUserRole } from "@/hooks/useUserRole"
import { 
  Brain, 
  Search, 
  FileText, 
  Gavel, 
  TrendingUp, 
  Clock, 
  Activity,
  BarChart3
} from "lucide-react"

export function DashboardContent() {
  const { isOwner, loading } = useUserRole()

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-muted rounded w-1/3 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-muted rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  // Show owner dashboard if user is owner
  if (isOwner) {
    return <OwnerDashboard />
  }

  // Regular dashboard for non-owner users
  const stats = [
    {
      title: "Casos Ativos",
      value: "48",
      change: "+12% em relação ao mês anterior",
      icon: BarChart3,
      color: "blue" as const
    },
    {
      title: "Consultas IA",
      value: "12",
      change: "Tempo médio: 2,4 min",
      icon: Brain,
      color: "purple" as const
    },
    {
      title: "Documentos Analisados",
      value: "156",
      change: "+15,3% este mês",
      icon: FileText,
      color: "green" as const
    },
    {
      title: "Peças Geradas",
      value: "89",
      change: "Eficiência aumentada",
      icon: Gavel,
      color: "orange" as const
    }
  ]

  const quickActions = [
    {
      title: "IA Jurídica",
      description: "Faça uma consulta",
      icon: Brain,
      color: "blue" as const
    },
    {
      title: "Jurisprudência",
      description: "Buscar decisões",
      icon: Search,
      color: "green" as const
    },
    {
      title: "Análise Documental",
      description: "Analisar documento",
      icon: FileText,
      color: "purple" as const
    },
    {
      title: "Gerador de Peças",
      description: "Criar documento",
      icon: Gavel,
      color: "orange" as const
    }
  ]

  const recentActivity = [
    {
      action: "Consulta IA realizada",
      time: "2 min atrás",
      status: "success"
    },
    {
      action: "Documento analisado",
      time: "15 min atrás",
      status: "pending"
    },
    {
      action: "Peça jurídica gerada",
      time: "1 hora atrás",
      status: "success"
    },
    {
      action: "Nova jurisprudência encontrada",
      time: "3 horas atrás",
      status: "success"
    }
  ]

  const systemStatus = [
    { service: "IA Jurídica", status: "Operacional" },
    { service: "Jurisprudência", status: "Operacional" },
    { service: "Análise Documental", status: "Operacional" },
    { service: "Gerador", status: "Operacional" }
  ]

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
        <p className="text-muted-foreground">
          Bem-vindo de volta! Aqui está um resumo das suas atividades.
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <GlowCard 
            key={index} 
            glowColor={stat.color}
            customSize
            className="w-full h-32"
          >
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <p className="text-sm font-medium text-muted-foreground">{stat.title}</p>
                <stat.icon className="h-4 w-4 text-muted-foreground" />
              </div>
              <div>
                <p className="text-2xl font-bold">{stat.value}</p>
                <p className="text-xs text-muted-foreground">{stat.change}</p>
              </div>
            </div>
          </GlowCard>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Quick Actions */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Ações Rápidas</CardTitle>
              <CardDescription>
                Acesse rapidamente as principais funcionalidades
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {quickActions.map((action, index) => (
                  <GlowCard 
                    key={index} 
                    glowColor={action.color}
                    customSize
                    className="w-full h-24 cursor-pointer hover:scale-105 transition-transform"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="p-2 rounded-lg bg-primary/10">
                        <action.icon className="h-6 w-6 text-primary" />
                      </div>
                      <div>
                        <h3 className="font-semibold">{action.title}</h3>
                        <p className="text-sm text-muted-foreground">{action.description}</p>
                      </div>
                    </div>
                  </GlowCard>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Atividade Recente</CardTitle>
              <CardDescription>
                Suas últimas ações na plataforma
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivity.map((activity, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <div className={`w-2 h-2 rounded-full ${
                      activity.status === 'success' ? 'bg-green-500' : 
                      activity.status === 'pending' ? 'bg-yellow-500' : 'bg-red-500'
                    }`} />
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium">{activity.action}</p>
                      <p className="text-xs text-muted-foreground">{activity.time}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* System Status */}
      <Card>
        <CardHeader>
          <CardTitle>Status do Sistema</CardTitle>
          <CardDescription>
            Monitoramento em tempo real dos serviços
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {systemStatus.map((system, index) => (
              <div key={index} className="flex items-center justify-between p-3 rounded-lg border">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse" />
                  <span className="font-medium">{system.service}</span>
                </div>
                <Badge variant="secondary" className="text-green-700 bg-green-100 dark:text-green-300 dark:bg-green-900/20">
                  {system.status}
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}