import { useState } from 'react'
import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { GlowCard } from '@/components/ui/glow-card'
import { But<PERSON> } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Settings, Server, Mail, Shield, Database, Globe } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

export default function ConfiguracoesSistema() {
  const [systemSettings, setSystemSettings] = useState({
    siteName: 'Advocacia Jurídica',
    siteDescription: 'Sistema de gestão jurídica completo',
    maintenanceMode: false,
    registrationEnabled: true,
    emailVerificationRequired: true,
    maxFileSize: 10, // MB
    sessionTimeout: 60, // minutes
    backupEnabled: true,
    backupFrequency: 'daily',
    emailProvider: 'smtp',
    smtpHost: '',
    smtpPort: 587,
    smtpUser: '',
    smtpPassword: '',
    timezone: 'America/Sao_Paulo',
    language: 'pt-BR',
    currency: 'BRL'
  })

  const [securitySettings, setSecuritySettings] = useState({
    passwordMinLength: 8,
    passwordRequireSpecialChars: true,
    passwordRequireNumbers: true,
    passwordRequireUppercase: true,
    twoFactorEnabled: false,
    maxLoginAttempts: 5,
    ipWhitelistEnabled: false,
    ipWhitelist: '',
    corsEnabled: true,
    corsOrigins: 'https://localhost:3000'
  })

  const { toast } = useToast()

  const handleSystemSettingChange = (key: string, value: any) => {
    setSystemSettings(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const handleSecuritySettingChange = (key: string, value: any) => {
    setSecuritySettings(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const saveSettings = async () => {
    try {
      // Aqui você implementaria a lógica para salvar as configurações no banco
      // Por exemplo, usando uma tabela de configurações
      
      toast({
        title: "Configurações salvas",
        description: "As configurações do sistema foram atualizadas com sucesso.",
      })
    } catch (error) {
      toast({
        title: "Erro",
        description: "Não foi possível salvar as configurações.",
        variant: "destructive",
      })
    }
  }

  const testEmailSettings = async () => {
    try {
      toast({
        title: "Testando configurações de email",
        description: "Um email de teste foi enviado.",
      })
    } catch (error) {
      toast({
        title: "Erro no teste de email",
        description: "Não foi possível enviar o email de teste.",
        variant: "destructive",
      })
    }
  }

  const resetToDefaults = () => {
    setSystemSettings({
      siteName: 'Advocacia Jurídica',
      siteDescription: 'Sistema de gestão jurídica completo',
      maintenanceMode: false,
      registrationEnabled: true,
      emailVerificationRequired: true,
      maxFileSize: 10,
      sessionTimeout: 60,
      backupEnabled: true,
      backupFrequency: 'daily',
      emailProvider: 'smtp',
      smtpHost: '',
      smtpPort: 587,
      smtpUser: '',
      smtpPassword: '',
      timezone: 'America/Sao_Paulo',
      language: 'pt-BR',
      currency: 'BRL'
    })
    
    setSecuritySettings({
      passwordMinLength: 8,
      passwordRequireSpecialChars: true,
      passwordRequireNumbers: true,
      passwordRequireUppercase: true,
      twoFactorEnabled: false,
      maxLoginAttempts: 5,
      ipWhitelistEnabled: false,
      ipWhitelist: '',
      corsEnabled: true,
      corsOrigins: 'https://localhost:3000'
    })

    toast({
      title: "Configurações resetadas",
      description: "Todas as configurações foram restauradas para os valores padrão.",
    })
  }

  return (
    <DashboardLayout title="Configurações do Sistema">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Settings className="h-6 w-6 text-primary" />
            <h2 className="text-2xl font-bold">Configurações do Sistema</h2>
          </div>
          <Badge variant="outline" className="text-sm">
            Ambiente: Produção
          </Badge>
        </div>

        <Tabs defaultValue="general" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="general">Geral</TabsTrigger>
            <TabsTrigger value="security">Segurança</TabsTrigger>
            <TabsTrigger value="email">Email</TabsTrigger>
            <TabsTrigger value="backup">Backup</TabsTrigger>
          </TabsList>

          <TabsContent value="general" className="space-y-6">
            {/* General Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Globe className="h-5 w-5" />
                  Configurações Gerais
                </CardTitle>
                <CardDescription>
                  Configurações básicas do sistema e interface
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="siteName">Nome do Sistema</Label>
                    <Input
                      id="siteName"
                      value={systemSettings.siteName}
                      onChange={(e) => handleSystemSettingChange('siteName', e.target.value)}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="timezone">Fuso Horário</Label>
                    <Select value={systemSettings.timezone} onValueChange={(value) => handleSystemSettingChange('timezone', value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="America/Sao_Paulo">São Paulo (GMT-3)</SelectItem>
                        <SelectItem value="America/New_York">Nova York (GMT-5)</SelectItem>
                        <SelectItem value="Europe/London">Londres (GMT+0)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="siteDescription">Descrição do Sistema</Label>
                  <Textarea
                    id="siteDescription"
                    value={systemSettings.siteDescription}
                    onChange={(e) => handleSystemSettingChange('siteDescription', e.target.value)}
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="language">Idioma</Label>
                    <Select value={systemSettings.language} onValueChange={(value) => handleSystemSettingChange('language', value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="pt-BR">Português (Brasil)</SelectItem>
                        <SelectItem value="en-US">English (US)</SelectItem>
                        <SelectItem value="es-ES">Español</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label htmlFor="currency">Moeda</Label>
                    <Select value={systemSettings.currency} onValueChange={(value) => handleSystemSettingChange('currency', value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="BRL">Real (R$)</SelectItem>
                        <SelectItem value="USD">Dólar ($)</SelectItem>
                        <SelectItem value="EUR">Euro (€)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="maintenanceMode">Modo de Manutenção</Label>
                      <p className="text-sm text-muted-foreground">
                        Quando ativo, apenas administradores podem acessar o sistema
                      </p>
                    </div>
                    <Switch
                      id="maintenanceMode"
                      checked={systemSettings.maintenanceMode}
                      onCheckedChange={(checked) => handleSystemSettingChange('maintenanceMode', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="registrationEnabled">Registro de Novos Usuários</Label>
                      <p className="text-sm text-muted-foreground">
                        Permite que novos usuários se registrem no sistema
                      </p>
                    </div>
                    <Switch
                      id="registrationEnabled"
                      checked={systemSettings.registrationEnabled}
                      onCheckedChange={(checked) => handleSystemSettingChange('registrationEnabled', checked)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="security" className="space-y-6">
            {/* Security Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  Configurações de Segurança
                </CardTitle>
                <CardDescription>
                  Configure políticas de segurança e autenticação
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="passwordMinLength">Tamanho Mínimo da Senha</Label>
                    <Input
                      id="passwordMinLength"
                      type="number"
                      value={securitySettings.passwordMinLength}
                      onChange={(e) => handleSecuritySettingChange('passwordMinLength', parseInt(e.target.value))}
                      min="6"
                      max="20"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="maxLoginAttempts">Máximo de Tentativas de Login</Label>
                    <Input
                      id="maxLoginAttempts"
                      type="number"
                      value={securitySettings.maxLoginAttempts}
                      onChange={(e) => handleSecuritySettingChange('maxLoginAttempts', parseInt(e.target.value))}
                      min="3"
                      max="10"
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="passwordRequireSpecialChars">Exigir Caracteres Especiais</Label>
                      <p className="text-sm text-muted-foreground">
                        Senha deve conter ao menos um caractere especial
                      </p>
                    </div>
                    <Switch
                      id="passwordRequireSpecialChars"
                      checked={securitySettings.passwordRequireSpecialChars}
                      onCheckedChange={(checked) => handleSecuritySettingChange('passwordRequireSpecialChars', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="passwordRequireNumbers">Exigir Números</Label>
                      <p className="text-sm text-muted-foreground">
                        Senha deve conter ao menos um número
                      </p>
                    </div>
                    <Switch
                      id="passwordRequireNumbers"
                      checked={securitySettings.passwordRequireNumbers}
                      onCheckedChange={(checked) => handleSecuritySettingChange('passwordRequireNumbers', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="twoFactorEnabled">Autenticação de Dois Fatores</Label>
                      <p className="text-sm text-muted-foreground">
                        Exigir 2FA para todos os usuários
                      </p>
                    </div>
                    <Switch
                      id="twoFactorEnabled"
                      checked={securitySettings.twoFactorEnabled}
                      onCheckedChange={(checked) => handleSecuritySettingChange('twoFactorEnabled', checked)}
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="ipWhitelist">Lista de IPs Permitidos</Label>
                  <Textarea
                    id="ipWhitelist"
                    value={securitySettings.ipWhitelist}
                    onChange={(e) => handleSecuritySettingChange('ipWhitelist', e.target.value)}
                    placeholder="***********&#10;********&#10;..."
                    rows={3}
                  />
                  <p className="text-sm text-muted-foreground mt-1">
                    Um IP por linha. Deixe vazio para permitir todos os IPs.
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="email" className="space-y-6">
            {/* Email Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Mail className="h-5 w-5" />
                  Configurações de Email
                </CardTitle>
                <CardDescription>
                  Configure o servidor de email para notificações
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="emailProvider">Provedor de Email</Label>
                  <Select value={systemSettings.emailProvider} onValueChange={(value) => handleSystemSettingChange('emailProvider', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="smtp">SMTP Personalizado</SelectItem>
                      <SelectItem value="gmail">Gmail</SelectItem>
                      <SelectItem value="outlook">Outlook</SelectItem>
                      <SelectItem value="sendgrid">SendGrid</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="smtpHost">Servidor SMTP</Label>
                    <Input
                      id="smtpHost"
                      value={systemSettings.smtpHost}
                      onChange={(e) => handleSystemSettingChange('smtpHost', e.target.value)}
                      placeholder="smtp.gmail.com"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="smtpPort">Porta SMTP</Label>
                    <Input
                      id="smtpPort"
                      type="number"
                      value={systemSettings.smtpPort}
                      onChange={(e) => handleSystemSettingChange('smtpPort', parseInt(e.target.value))}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="smtpUser">Usuário SMTP</Label>
                    <Input
                      id="smtpUser"
                      value={systemSettings.smtpUser}
                      onChange={(e) => handleSystemSettingChange('smtpUser', e.target.value)}
                      placeholder="<EMAIL>"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="smtpPassword">Senha SMTP</Label>
                    <Input
                      id="smtpPassword"
                      type="password"
                      value={systemSettings.smtpPassword}
                      onChange={(e) => handleSystemSettingChange('smtpPassword', e.target.value)}
                      placeholder="••••••••"
                    />
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="emailVerificationRequired">Verificação de Email Obrigatória</Label>
                    <p className="text-sm text-muted-foreground">
                      Usuários devem verificar email antes de acessar o sistema
                    </p>
                  </div>
                  <Switch
                    id="emailVerificationRequired"
                    checked={systemSettings.emailVerificationRequired}
                    onCheckedChange={(checked) => handleSystemSettingChange('emailVerificationRequired', checked)}
                  />
                </div>

                <div className="pt-4 border-t">
                  <Button onClick={testEmailSettings} variant="outline" className="w-full">
                    Testar Configurações de Email
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="backup" className="space-y-6">
            {/* Backup Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="h-5 w-5" />
                  Configurações de Backup
                </CardTitle>
                <CardDescription>
                  Configure backups automáticos e restauração de dados
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="backupEnabled">Backup Automático</Label>
                    <p className="text-sm text-muted-foreground">
                      Realizar backups automáticos do banco de dados
                    </p>
                  </div>
                  <Switch
                    id="backupEnabled"
                    checked={systemSettings.backupEnabled}
                    onCheckedChange={(checked) => handleSystemSettingChange('backupEnabled', checked)}
                  />
                </div>

                <div>
                  <Label htmlFor="backupFrequency">Frequência do Backup</Label>
                  <Select value={systemSettings.backupFrequency} onValueChange={(value) => handleSystemSettingChange('backupFrequency', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="hourly">A cada hora</SelectItem>
                      <SelectItem value="daily">Diário</SelectItem>
                      <SelectItem value="weekly">Semanal</SelectItem>
                      <SelectItem value="monthly">Mensal</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-3">
                  <Button 
                    className="w-full"
                    onClick={() => toast({
                      title: "Backup iniciado",
                      description: "O backup foi iniciado e será executado em segundo plano.",
                    })}
                  >
                    Realizar Backup Agora
                  </Button>
                  <Button 
                    variant="outline" 
                    className="w-full"
                    onClick={() => toast({
                      title: "Redirecionando",
                      description: "Abrindo página de gerenciamento de backups...",
                    })}
                  >
                    Visualizar Backups Existentes
                  </Button>
                  <Button 
                    variant="destructive" 
                    className="w-full"
                    onClick={() => toast({
                      title: "Atenção",
                      description: "A restauração de backup deve ser feita com cuidado. Contate o suporte.",
                      variant: "destructive",
                    })}
                  >
                    Restaurar do Backup
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Action Buttons */}
        <div className="flex justify-between">
          <Button onClick={resetToDefaults} variant="outline" size="lg">
            Resetar Padrões
          </Button>
          <Button onClick={saveSettings} size="lg">
            Salvar Configurações
          </Button>
        </div>
      </div>
    </DashboardLayout>
  )
}