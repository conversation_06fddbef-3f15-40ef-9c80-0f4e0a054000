import { useState, useEffect } from 'react'
import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { GlowCard } from '@/components/ui/glow-card'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Input } from '@/components/ui/input'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Building, Plus, Search, Users, Settings, Crown } from 'lucide-react'
import { supabase } from '@/integrations/supabase/client'
import { useToast } from '@/hooks/use-toast'

interface Organization {
  id: string
  name: string
  subscription_plan: string
  max_users: number
  created_at: string
  owner_id: string
  member_count?: number
}

export default function GerenciarOrganizacoes() {
  const [organizations, setOrganizations] = useState<Organization[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [newOrgName, setNewOrgName] = useState('')
  const [newOrgPlan, setNewOrgPlan] = useState('basic')
  const [newOrgMaxUsers, setNewOrgMaxUsers] = useState(5)
  const { toast } = useToast()

  useEffect(() => {
    fetchOrganizations()
  }, [])

  const fetchOrganizations = async () => {
    try {
      const { data: orgs, error } = await supabase
        .from('organizations')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) throw error

      // Fetch member count for each organization
      const orgsWithMembers = await Promise.all(
        (orgs || []).map(async (org) => {
          const { count } = await supabase
            .from('organization_members')
            .select('*', { count: 'exact', head: true })
            .eq('organization_id', org.id)

          return {
            ...org,
            member_count: count || 0
          }
        })
      )

      setOrganizations(orgsWithMembers)
    } catch (error) {
      console.error('Error fetching organizations:', error)
      toast({
        title: "Erro",
        description: "Não foi possível carregar as organizações.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const createOrganization = async () => {
    try {
      const { error } = await supabase
        .from('organizations')
        .insert([{
          name: newOrgName,
          subscription_plan: newOrgPlan,
          max_users: newOrgMaxUsers,
          owner_id: (await supabase.auth.getUser()).data.user?.id
        }])

      if (error) throw error

      await fetchOrganizations()
      setIsCreateDialogOpen(false)
      setNewOrgName('')
      setNewOrgPlan('basic')
      setNewOrgMaxUsers(5)
      
      toast({
        title: "Sucesso",
        description: "Organização criada com sucesso.",
      })
    } catch (error) {
      console.error('Error creating organization:', error)
      toast({
        title: "Erro",
        description: "Não foi possível criar a organização.",
        variant: "destructive",
      })
    }
  }

  const editOrganization = (orgId: string) => {
    toast({
      title: "Editar Organização",
      description: "Função de edição será implementada em breve.",
    })
  }

  const manageMembers = (orgId: string) => {
    toast({
      title: "Gerenciar Membros",
      description: "Função de gerenciamento de membros será implementada em breve.",
    })
  }

  const transferOwnership = (orgId: string) => {
    toast({
      title: "Transferir Propriedade",
      description: "Função de transferência de propriedade será implementada em breve.",
    })
  }

  const filteredOrganizations = organizations.filter(org =>
    org.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const getPlanBadgeVariant = (plan: string) => {
    switch (plan) {
      case 'enterprise': return 'default'
      case 'professional': return 'secondary'
      case 'basic': return 'outline'
      default: return 'outline'
    }
  }

  const getPlanLabel = (plan: string) => {
    switch (plan) {
      case 'enterprise': return 'Enterprise'
      case 'professional': return 'Professional'
      case 'basic': return 'Básico'
      default: return 'Básico'
    }
  }

  if (loading) {
    return (
      <DashboardLayout title="Gerenciar Organizações">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-muted rounded w-1/4"></div>
          <div className="h-64 bg-muted rounded"></div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout title="Gerenciar Organizações">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Building className="h-6 w-6 text-primary" />
            <h2 className="text-2xl font-bold">Gerenciar Organizações</h2>
          </div>
          
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                Nova Organização
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Criar Nova Organização</DialogTitle>
                <DialogDescription>
                  Preencha os dados para criar uma nova organização no sistema.
                </DialogDescription>
              </DialogHeader>
              
              <div className="space-y-4">
                <div>
                  <Label htmlFor="orgName">Nome da Organização</Label>
                  <Input
                    id="orgName"
                    value={newOrgName}
                    onChange={(e) => setNewOrgName(e.target.value)}
                    placeholder="Digite o nome da organização"
                  />
                </div>
                
                <div>
                  <Label htmlFor="orgPlan">Plano de Assinatura</Label>
                  <Select value={newOrgPlan} onValueChange={setNewOrgPlan}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="basic">Básico</SelectItem>
                      <SelectItem value="professional">Professional</SelectItem>
                      <SelectItem value="enterprise">Enterprise</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label htmlFor="maxUsers">Limite de Usuários</Label>
                  <Input
                    id="maxUsers"
                    type="number"
                    value={newOrgMaxUsers}
                    onChange={(e) => setNewOrgMaxUsers(Number(e.target.value))}
                    min="1"
                    max="1000"
                  />
                </div>
              </div>
              
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  Cancelar
                </Button>
                <Button onClick={createOrganization} disabled={!newOrgName.trim()}>
                  Criar Organização
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        {/* Search */}
        <Card>
          <CardHeader>
            <CardTitle>Buscar Organizações</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Buscar por nome da organização..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </CardContent>
        </Card>

        {/* Organizations Table */}
        <Card>
          <CardHeader>
            <CardTitle>Organizações ({filteredOrganizations.length})</CardTitle>
            <CardDescription>
              Gerencie todas as organizações e seus membros
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nome</TableHead>
                  <TableHead>Plano</TableHead>
                  <TableHead>Membros</TableHead>
                  <TableHead>Limite</TableHead>
                  <TableHead>Data de Criação</TableHead>
                  <TableHead>Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredOrganizations.map((org) => (
                  <TableRow key={org.id}>
                    <TableCell className="font-medium">
                      <div className="flex items-center gap-2">
                        <Building className="h-4 w-4 text-muted-foreground" />
                        {org.name}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={getPlanBadgeVariant(org.subscription_plan)}>
                        {getPlanLabel(org.subscription_plan)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Users className="h-4 w-4 text-muted-foreground" />
                        {org.member_count}
                      </div>
                    </TableCell>
                    <TableCell>{org.max_users}</TableCell>
                    <TableCell>
                      {new Date(org.created_at).toLocaleDateString('pt-BR')}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => editOrganization(org.id)}
                        >
                          <Settings className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => manageMembers(org.id)}
                        >
                          <Users className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => transferOwnership(org.id)}
                        >
                          <Crown className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}