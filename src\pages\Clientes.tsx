import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { GlowCard } from "@/components/ui/glow-card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Plus, Search, Filter } from "lucide-react"
import { Input } from "@/components/ui/input"

export default function Clientes() {
  return (
    <DashboardLayout title="Clientes">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Gestão de Clientes</h2>
            <p className="text-muted-foreground">
              Gerencie seus clientes e suas informações
            </p>
          </div>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Novo Cliente
          </Button>
        </div>

        <div className="flex items-center space-x-2">
          <div className="relative flex-1">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input placeholder="Buscar clientes..." className="pl-8" />
          </div>
          <Button variant="outline">
            <Filter className="mr-2 h-4 w-4" />
            Filtros
          </Button>
        </div>

        <div className="grid gap-4">
          {[
            { name: "Cliente 1", color: "blue" as const },
            { name: "Cliente 2", color: "green" as const },
            { name: "Cliente 3", color: "purple" as const }
          ].map((client, i) => (
            <GlowCard key={i} glowColor={client.color} customSize className="w-full">
              <div className="p-6">
                <CardTitle className="mb-2">{client.name}</CardTitle>
                <CardDescription className="mb-4">
                  Informações do cliente exemplo
                </CardDescription>
                <p className="text-muted-foreground">
                  Dados do cliente serão exibidos aqui quando implementados.
                </p>
              </div>
            </GlowCard>
          ))}
        </div>
      </div>
    </DashboardLayout>
  )
}