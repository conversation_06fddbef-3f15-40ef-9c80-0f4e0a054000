import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { GlowCard } from "@/components/ui/glow-card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, <PERSON><PERSON>hart, Download, Calendar, TrendingUp } from "lucide-react"

export default function Relatorios() {
  return (
    <DashboardLayout title="Relatórios">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Relatórios e Análises</h2>
            <p className="text-muted-foreground">
              Visualize dados e métricas do seu escritório
            </p>
          </div>
          <Button>
            <Download className="mr-2 h-4 w-4" />
            Exportar Relatório
          </Button>
        </div>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <GlowCard glowColor="blue" customSize className="w-full h-32">
            <div className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total de Casos
              </CardTitle>
              <BarChart className="h-4 w-4 text-muted-foreground" />
            </div>
            <div>
              <div className="text-2xl font-bold">245</div>
              <p className="text-xs text-muted-foreground">
                +20.1% em relação ao mês passado
              </p>
            </div>
          </GlowCard>

          <GlowCard glowColor="green" customSize className="w-full h-32">
            <div className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Casos Ativos
              </CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </div>
            <div>
              <div className="text-2xl font-bold">89</div>
              <p className="text-xs text-muted-foreground">
                +5.3% em relação ao mês passado
              </p>
            </div>
          </GlowCard>

          <GlowCard glowColor="purple" customSize className="w-full h-32">
            <div className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Taxa de Sucesso
              </CardTitle>
              <PieChart className="h-4 w-4 text-muted-foreground" />
            </div>
            <div>
              <div className="text-2xl font-bold">87.2%</div>
              <p className="text-xs text-muted-foreground">
                +2.1% em relação ao mês passado
              </p>
            </div>
          </GlowCard>

          <GlowCard glowColor="orange" customSize className="w-full h-32">
            <div className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
              Receita Mensal
              </CardTitle>
              <LineChart className="h-4 w-4 text-muted-foreground" />
            </div>
            <div>
              <div className="text-2xl font-bold">R$ 45.2k</div>
              <p className="text-xs text-muted-foreground">
                +15.8% em relação ao mês passado
              </p>
            </div>
          </GlowCard>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart className="mr-2 h-4 w-4" />
                Casos por Área
              </CardTitle>
              <CardDescription>
                Distribuição de casos por área do direito
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[200px] flex items-center justify-center border-2 border-dashed border-muted-foreground/25 rounded-lg">
                <p className="text-muted-foreground">Gráfico será exibido aqui</p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calendar className="mr-2 h-4 w-4" />
                Evolução Temporal
              </CardTitle>
              <CardDescription>
                Evolução de casos ao longo do tempo
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[200px] flex items-center justify-center border-2 border-dashed border-muted-foreground/25 rounded-lg">
                <p className="text-muted-foreground">Gráfico será exibido aqui</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  )
}