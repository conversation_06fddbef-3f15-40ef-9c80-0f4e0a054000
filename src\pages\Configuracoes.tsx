import { useState, useEffect } from 'react'
import { useSearchParams } from 'react-router-dom'
import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { GlowCard } from '@/components/ui/glow-card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Separator } from '@/components/ui/separator'
import { ThemeToggle } from '@/components/theme-toggle'
import { User, Shield, Bell, CreditCard, <PERSON>, Save, Check, Clock, AlertTriangle, Info } from 'lucide-react'
import { supabase } from '@/integrations/supabase/client'
import { useToast } from '@/hooks/use-toast'
import { useNotifications } from '@/hooks/useNotifications'

export default function Configuracoes() {
  const { toast } = useToast()
  const [searchParams, setSearchParams] = useSearchParams()
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const { notifications, unreadCount, markAsRead, markAllAsRead } = useNotifications()

  // Get active tab from URL params, default to 'perfil'
  const activeTab = searchParams.get('tab') || 'perfil'

  // Function to change tab and update URL
  const handleTabChange = (tab: string) => {
    setSearchParams({ tab })
  }

  // Function to get notification icon
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "success":
        return <Check className="h-4 w-4 text-green-500" />
      case "warning":
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      case "error":
        return <AlertTriangle className="h-4 w-4 text-red-500" />
      default:
        return <Info className="h-4 w-4 text-blue-500" />
    }
  }

  const [profileData, setProfileData] = useState({
    fullName: '',
    email: '',
    phone: '',
    oab: '',
    avatarUrl: ''
  })

  const [preferences, setPreferences] = useState({
    emailNotifications: true,
    pushNotifications: false,
    twoFactorAuth: false,
    darkMode: false,
    language: 'pt-BR',
    timezone: 'America/Sao_Paulo'
  })

  useEffect(() => {
    loadSettings()
  }, [])

  const loadSettings = async () => {
    try {
      setLoading(true)
      
      // Load user profile
      const { data: { user } } = await supabase.auth.getUser()
      if (user) {
        const { data: profile } = await supabase
          .from('profiles')
          .select('*')
          .eq('user_id', user.id)
          .single()

        if (profile) {
          setProfileData({
            fullName: profile.full_name || '',
            email: profile.email || user.email || '',
            phone: '',
            oab: '',
            avatarUrl: profile.avatar_url || ''
          })
        }
      }
      
    } catch (error) {
      console.error('Error loading settings:', error)
      toast({
        title: "Erro",
        description: "Não foi possível carregar as configurações.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const saveProfile = async () => {
    try {
      setSaving(true)
      
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('Usuário não autenticado')

      const { error } = await supabase
        .from('profiles')
        .update({
          full_name: profileData.fullName,
          avatar_url: profileData.avatarUrl,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', user.id)

      if (error) throw error

      toast({
        title: "Sucesso",
        description: "Perfil atualizado com sucesso.",
      })
    } catch (error) {
      console.error('Error saving profile:', error)
      toast({
        title: "Erro",
        description: "Não foi possível salvar o perfil.",
        variant: "destructive",
      })
    } finally {
      setSaving(false)
    }
  }

  const savePreferences = async () => {
    try {
      setSaving(true)
      
      toast({
        title: "Sucesso",
        description: "Preferências salvas com sucesso.",
      })
    } catch (error) {
      console.error('Error saving preferences:', error)
      toast({
        title: "Erro",
        description: "Não foi possível salvar as preferências.",
        variant: "destructive",
      })
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <DashboardLayout title="Configurações">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-muted rounded w-1/4"></div>
          <div className="h-64 bg-muted rounded"></div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout title="Configurações">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Configurações</h2>
            <p className="text-muted-foreground">
              Gerencie suas configurações e preferências
            </p>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="perfil" className="flex items-center gap-2">
              <User className="h-4 w-4" />
              <span className="hidden sm:inline">Perfil</span>
            </TabsTrigger>
            <TabsTrigger value="seguranca" className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              <span className="hidden sm:inline">Segurança</span>
            </TabsTrigger>
            <TabsTrigger value="notificacoes" className="flex items-center gap-2">
              <Bell className="h-4 w-4" />
              <span className="hidden sm:inline">Notificações</span>
            </TabsTrigger>
            <TabsTrigger value="assinatura" className="flex items-center gap-2">
              <CreditCard className="h-4 w-4" />
              <span className="hidden sm:inline">Assinatura</span>
            </TabsTrigger>
            <TabsTrigger value="privacidade" className="flex items-center gap-2">
              <Lock className="h-4 w-4" />
              <span className="hidden sm:inline">Privacidade</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="perfil" className="space-y-6">
            <GlowCard customSize className="w-full">
              <CardHeader>
                <CardTitle>Informações Pessoais</CardTitle>
                <CardDescription>
                  Atualize suas informações pessoais e de contato
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="nome">Nome Completo</Label>
                    <Input 
                      id="nome" 
                      value={profileData.fullName}
                      onChange={(e) => setProfileData(prev => ({ ...prev, fullName: e.target.value }))}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">E-mail</Label>
                    <Input 
                      id="email" 
                      type="email" 
                      value={profileData.email}
                      onChange={(e) => setProfileData(prev => ({ ...prev, email: e.target.value }))}
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="telefone">Telefone</Label>
                  <Input 
                    id="telefone" 
                    value={profileData.phone}
                    onChange={(e) => setProfileData(prev => ({ ...prev, phone: e.target.value }))}
                    placeholder="+55 (11) 99999-9999"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="oab">Número da OAB</Label>
                  <Input 
                    id="oab" 
                    value={profileData.oab}
                    onChange={(e) => setProfileData(prev => ({ ...prev, oab: e.target.value }))}
                    placeholder="123456/SP"
                  />
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Tema</Label>
                    <p className="text-sm text-muted-foreground">
                      Escolha entre tema claro e escuro
                    </p>
                  </div>
                  <ThemeToggle />
                </div>
                <Button 
                  onClick={saveProfile} 
                  disabled={saving}
                  className="flex items-center gap-2"
                >
                  <Save className="h-4 w-4" />
                  {saving ? 'Salvando...' : 'Salvar Perfil'}
                </Button>
              </CardContent>
            </GlowCard>
          </TabsContent>

          <TabsContent value="seguranca" className="space-y-6">
            <GlowCard customSize className="w-full" glowColor="red">
              <CardHeader>
                <CardTitle>Senha</CardTitle>
                <CardDescription>
                  Altere sua senha para manter sua conta segura
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="senha-atual">Senha Atual</Label>
                  <Input id="senha-atual" type="password" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="nova-senha">Nova Senha</Label>
                  <Input id="nova-senha" type="password" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="confirmar-senha">Confirmar Nova Senha</Label>
                  <Input id="confirmar-senha" type="password" />
                </div>
                <Button>Alterar Senha</Button>
              </CardContent>
            </GlowCard>

            <GlowCard customSize className="w-full" glowColor="orange">
              <CardHeader>
                <CardTitle>Autenticação de Dois Fatores</CardTitle>
                <CardDescription>
                  Adicione uma camada extra de segurança à sua conta
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>2FA Ativado</Label>
                    <p className="text-sm text-muted-foreground">
                      Autenticação de dois fatores está desativada
                    </p>
                  </div>
                  <Switch />
                </div>
                <Button variant="outline">Configurar 2FA</Button>
              </CardContent>
            </GlowCard>

            <GlowCard customSize className="w-full" glowColor="purple">
              <CardHeader>
                <CardTitle>Sessões Ativas</CardTitle>
                <CardDescription>
                  Gerencie suas sessões ativas em diferentes dispositivos
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <p className="font-medium">Chrome - Windows</p>
                      <p className="text-sm text-muted-foreground">Último acesso há 2 min - São Paulo, Brasil</p>
                    </div>
                    <Button variant="outline" size="sm">Encerrar</Button>
                  </div>
                  <Button variant="destructive" className="w-full">
                    Encerrar Todas as Sessões
                  </Button>
                </div>
              </CardContent>
            </GlowCard>
          </TabsContent>

          <TabsContent value="notificacoes" className="space-y-6">
            <GlowCard customSize className="w-full" glowColor="green">
              <CardHeader>
                <CardTitle>Notificações por E-mail</CardTitle>
                <CardDescription>
                  Configure quando você deseja receber notificações por e-mail
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Novos casos</Label>
                    <p className="text-sm text-muted-foreground">
                      Receber e-mail quando houver novos casos atribuídos
                    </p>
                  </div>
                  <Switch defaultChecked />
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Prazos importantes</Label>
                    <p className="text-sm text-muted-foreground">
                      Lembrete de prazos processuais importantes
                    </p>
                  </div>
                  <Switch defaultChecked />
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Relatórios semanais</Label>
                    <p className="text-sm text-muted-foreground">
                      Resumo semanal de atividades e métricas
                    </p>
                  </div>
                  <Switch />
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Atualizações do sistema</Label>
                    <p className="text-sm text-muted-foreground">
                      Notificações sobre novas funcionalidades e atualizações
                    </p>
                  </div>
                  <Switch defaultChecked />
                </div>
                <Button 
                  onClick={savePreferences} 
                  disabled={saving}
                  className="flex items-center gap-2"
                >
                  <Save className="h-4 w-4" />
                  {saving ? 'Salvando...' : 'Salvar Preferências'}
                </Button>
              </CardContent>
            </GlowCard>

            <GlowCard customSize className="w-full" glowColor="purple">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>Notificações Recentes</span>
                  <div className="flex items-center gap-2">
                    {unreadCount > 0 && (
                      <Badge variant="secondary">
                        {unreadCount} não lida{unreadCount > 1 ? 's' : ''}
                      </Badge>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={markAllAsRead}
                      disabled={unreadCount === 0}
                    >
                      Marcar todas como lidas
                    </Button>
                  </div>
                </CardTitle>
                <CardDescription>
                  Suas notificações mais recentes
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4 max-h-80 overflow-y-auto">
                  {notifications.slice(0, 5).map((notification) => (
                    <div
                      key={notification.id}
                      className={`flex items-start gap-3 p-3 rounded-lg border ${
                        !notification.read ? 'bg-muted/50 border-primary/20' : 'bg-muted/20'
                      }`}
                    >
                      <div className="mt-1">
                        {getNotificationIcon(notification.type)}
                      </div>
                      <div className="flex-1 space-y-1">
                        <div className="flex items-center justify-between">
                          <p className={`text-sm font-medium ${!notification.read ? 'text-foreground' : 'text-muted-foreground'}`}>
                            {notification.title}
                          </p>
                          {!notification.read && (
                            <div className="w-2 h-2 bg-primary rounded-full" />
                          )}
                        </div>
                        <p className="text-xs text-muted-foreground">
                          {notification.description}
                        </p>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-1 text-xs text-muted-foreground">
                            <Clock className="h-3 w-3" />
                            {notification.time}
                          </div>
                          {!notification.read && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => markAsRead(notification.id)}
                              className="text-xs h-6 px-2"
                            >
                              Marcar como lida
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                  {notifications.length === 0 && (
                    <div className="text-center py-8 text-muted-foreground">
                      <Bell className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p>Nenhuma notificação no momento</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </GlowCard>

            <GlowCard customSize className="w-full" glowColor="blue">
              <CardHeader>
                <CardTitle>Notificações Push</CardTitle>
                <CardDescription>
                  Configure notificações em tempo real no navegador
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Ativar notificações push</Label>
                    <p className="text-sm text-muted-foreground">
                      Receber notificações em tempo real no navegador
                    </p>
                  </div>
                  <Switch />
                </div>
                <Button variant="outline">Configurar Notificações</Button>
              </CardContent>
            </GlowCard>
          </TabsContent>

          <TabsContent value="assinatura" className="space-y-6">
            <GlowCard customSize className="w-full" glowColor="purple">
              <CardHeader>
                <CardTitle>Plano Atual</CardTitle>
                <CardDescription>
                  Gerencie sua assinatura e método de pagamento
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="border rounded-lg p-4 bg-muted/50">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-lg font-semibold">Plano Profissional</h3>
                    <span className="bg-primary text-primary-foreground px-2 py-1 rounded text-sm">
                      Ativo
                    </span>
                  </div>
                  <p className="text-sm text-muted-foreground mb-4">
                    Acesso completo a todas as funcionalidades
                  </p>
                  <div className="flex items-center justify-between">
                    <span className="text-2xl font-bold">R$ 299/mês</span>
                    <Button variant="outline">Alterar Plano</Button>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <h4 className="font-medium">Próxima cobrança</h4>
                  <p className="text-sm text-muted-foreground">
                    15 de dezembro de 2024 - R$ 299,00
                  </p>
                </div>

                <Separator />

                <div className="space-y-4">
                  <h4 className="font-medium">Método de pagamento</h4>
                  <div className="border rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div className="w-8 h-5 bg-blue-600 rounded"></div>
                        <span>•••• •••• •••• 4242</span>
                      </div>
                      <Button variant="outline" size="sm">Editar</Button>
                    </div>
                  </div>
                </div>

                <div className="flex space-x-2">
                  <Button variant="outline" className="flex-1">Histórico de Faturas</Button>
                  <Button variant="destructive" className="flex-1">Cancelar Assinatura</Button>
                </div>
              </CardContent>
            </GlowCard>
          </TabsContent>

          <TabsContent value="privacidade" className="space-y-6">
            <GlowCard customSize className="w-full" glowColor="orange">
              <CardHeader>
                <CardTitle>Dados Pessoais</CardTitle>
                <CardDescription>
                  Controle como seus dados são coletados e utilizados
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Coleta de dados analíticos</Label>
                    <p className="text-sm text-muted-foreground">
                      Permitir coleta de dados para melhorar o serviço
                    </p>
                  </div>
                  <Switch defaultChecked />
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Cookies funcionais</Label>
                    <p className="text-sm text-muted-foreground">
                      Necessários para o funcionamento do sistema
                    </p>
                  </div>
                  <Switch defaultChecked disabled />
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Marketing personalizado</Label>
                    <p className="text-sm text-muted-foreground">
                      Receber conteúdo personalizado baseado no seu uso
                    </p>
                  </div>
                  <Switch />
                </div>
              </CardContent>
            </GlowCard>

            <GlowCard customSize className="w-full" glowColor="red">
              <CardHeader>
                <CardTitle>Controle de Dados</CardTitle>
                <CardDescription>
                  Gerencie e exporte seus dados pessoais
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button variant="outline" className="w-full">
                  Baixar Meus Dados
                </Button>
                <Button 
                  variant="destructive" 
                  className="w-full"
                  disabled={saving}
                  onClick={() => {
                    toast({
                      title: "Atenção",
                      description: "A funcionalidade de exclusão de conta será implementada em breve.",
                    })
                  }}
                >
                  Excluir Conta
                </Button>
                <p className="text-xs text-muted-foreground">
                  A exclusão da conta é permanente e não pode ser desfeita. Todos os dados serão removidos em até 30 dias.
                </p>
              </CardContent>
            </GlowCard>

            <GlowCard customSize className="w-full" glowColor="green">
              <CardHeader>
                <CardTitle>Visibilidade do Perfil</CardTitle>
                <CardDescription>
                  Configure quem pode ver suas informações
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Perfil público</Label>
                    <p className="text-sm text-muted-foreground">
                      Permitir que outros usuários vejam seu perfil
                    </p>
                  </div>
                  <Switch />
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Mostrar estatísticas</Label>
                    <p className="text-sm text-muted-foreground">
                      Exibir estatísticas de casos no perfil público
                    </p>
                  </div>
                  <Switch />
                </div>
              </CardContent>
            </GlowCard>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}