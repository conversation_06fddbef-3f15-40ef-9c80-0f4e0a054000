import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { GlowCard } from "@/components/ui/glow-card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Plus, Calendar, Clock } from "lucide-react"

export default function Agenda() {
  return (
    <DashboardLayout title="Agenda">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Agenda</h2>
            <p className="text-muted-foreground">
              Gerencie seus compromissos e eventos
            </p>
          </div>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Novo Evento
          </Button>
        </div>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <GlowCard glowColor="blue" customSize className="w-full">
            <div className="p-6">
              <CardTitle className="flex items-center mb-2">
                <Calendar className="mr-2 h-4 w-4" />
                Hoje
              </CardTitle>
              <CardDescription className="mb-4">
                Compromissos de hoje
              </CardDescription>
              <p className="text-muted-foreground">
                Nenhum compromisso agendado para hoje.
              </p>
            </div>
          </GlowCard>

          <GlowCard glowColor="green" customSize className="w-full">
            <div className="p-6">
              <CardTitle className="flex items-center mb-2">
                <Clock className="mr-2 h-4 w-4" />
                Próximos
              </CardTitle>
              <CardDescription className="mb-4">
                Próximos compromissos
              </CardDescription>
              <p className="text-muted-foreground">
                Próximos eventos aparecerão aqui.
              </p>
            </div>
          </GlowCard>

          <GlowCard glowColor="purple" customSize className="w-full">
            <div className="p-6">
              <CardTitle className="mb-2">Estatísticas</CardTitle>
              <CardDescription className="mb-4">
                Resumo da agenda
              </CardDescription>
              <p className="text-muted-foreground">
                Estatísticas da agenda serão exibidas aqui.
              </p>
            </div>
          </GlowCard>
        </div>
      </div>
    </DashboardLayout>
  )
}