import { useState } from "react"
import { NavLink, useLocation } from "react-router-dom"
import { 
  Scale, 
  BarChart3, 
  Brain, 
  Users, 
  Calendar, 
  Search, 
  FileText, 
  Gavel, 
  FileBarChart, 
  Settings, 
  Menu,
  X,
  User
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"

const menuItems = [
  { icon: BarChart3, label: "Dashboard", path: "/dashboard" },
  { icon: Brain, label: "IA Jurídica", path: "/ia-juridica" },
  { icon: Users, label: "Clientes", path: "/clientes" },
  { icon: Calendar, label: "Agenda", path: "/agenda" },
  { icon: Search, label: "Jurisprudência", path: "/jurisprudencia" },
  { icon: FileText, label: "Análise Documental", path: "/analise-documental" },
  { icon: Gavel, label: "Gerador de Peças", path: "/gerador-pecas" },
  { icon: <PERSON>Bar<PERSON>hart, label: "Re<PERSON><PERSON><PERSON>s", path: "/relatorios" },
  { icon: User, label: "Usu<PERSON>rio<PERSON>", path: "/usuarios" },
  { icon: Settings, label: "Configurações", path: "/configuracoes" },
]

interface SidebarProps {
  isOpen: boolean
  onToggle: () => void
}

export function Sidebar({ isOpen, onToggle }: SidebarProps) {
  const location = useLocation()

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black/50 z-40 lg:hidden"
          onClick={onToggle}
        />
      )}
      
      {/* Sidebar */}
      <aside className={cn(
        "fixed left-0 top-0 z-50 h-screen w-64 bg-card border-r transition-transform duration-300 ease-in-out lg:translate-x-0",
        isOpen ? "translate-x-0" : "-translate-x-full"
      )}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center space-x-2">
            <Scale className="h-8 w-8 text-primary" />
            <span className="text-xl font-bold bg-gradient-legal bg-clip-text text-transparent">
              LegalTech
            </span>
          </div>
          <Button 
            variant="ghost" 
            size="icon" 
            onClick={onToggle}
            className="lg:hidden"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>

        {/* Navigation */}
        <nav className="p-4 space-y-2">
          {menuItems.map((item) => (
            <NavLink
              key={item.path}
              to={item.path}
              className={({ isActive }) =>
                cn(
                  "flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors duration-200",
                  isActive
                    ? "bg-primary text-primary-foreground shadow-md"
                    : "text-muted-foreground hover:bg-primary/10 hover:text-primary"
                )
              }
              onClick={() => {
                // Close sidebar on mobile when clicking a link
                if (window.innerWidth < 1024) {
                  onToggle()
                }
              }}
            >
              <item.icon className="h-5 w-5" />
              <span className="font-medium">{item.label}</span>
            </NavLink>
          ))}
        </nav>

        {/* User info */}
        <div className="absolute bottom-4 left-4 right-4">
          <div className="p-3 bg-muted rounded-lg">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                <span className="text-sm font-semibold text-primary-foreground">RN</span>
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate">Rodolpho Neto</p>
                <p className="text-xs text-muted-foreground truncate">Escritório</p>
              </div>
            </div>
          </div>
        </div>
      </aside>
    </>
  )
}